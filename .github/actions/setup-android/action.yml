name: 'Setup Android Environment'
description: Shared setup for Ruby and Java environments

runs:
  using: "composite"
  steps:
    - name: 'Checkout sources'
      uses: actions/checkout@v4

    - name: 'Install gems'
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.3.7'
        bundler-cache: true

    - name: 'Set up JDK'
      uses: actions/setup-java@v4
      with:
        distribution: "temurin"
        java-version: 17
        cache: 'gradle'
