package com.ccaiplatform.ccaichat.model

import com.google.gson.Gson
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class WebFormTest {

    @Test
     fun chatMessageFormSerializationAndDeserialization() {
        // Given
        val originalForm = Form.WebForm(
            name = "Test Form",
            title = "Test Title",
            subtitle = "Test Subtitle",
            previewEndpoint = "https://test.com/preview",
            externalFormId = "form123",
            smartActionId = 1,
            image = "https://test.com/image.jpg"
        )

        // When
        val jsonString = Gson().toJson(originalForm)
        val deserializedForm = Gson().fromJson(jsonString, Form.WebForm::class.java)

        // Then
        assertNotNull(jsonString, "JSON string should not be null")
        assertEquals(originalForm.name, deserializedForm.name, "Name should match")
        assertEquals(originalForm.title, deserializedForm.title, "Title should match")
        assertEquals(originalForm.subtitle, deserializedForm.subtitle, "Subtitle should match")
        assertEquals(originalForm.previewEndpoint, deserializedForm.previewEndpoint, "Preview endpoint should match")
        assertEquals(originalForm.externalFormId, deserializedForm.externalFormId, "External form ID should match")
        assertEquals(originalForm.smartActionId, deserializedForm.smartActionId, "Smart action ID should match")
        assertEquals(originalForm.image, deserializedForm.image, "Image should match")
    }

    @Test
    fun chatMessageFormWithNullValues() {
        // Given
        val originalForm = Form.WebForm()

        // When
        val jsonString = Gson().toJson(originalForm)
        val deserializedForm = Gson().fromJson(jsonString, Form.WebForm::class.java)

        // Then
        assertNotNull(jsonString, "JSON string should not be null")
        assertNull(deserializedForm.name, "Name should be null")
        assertNull(deserializedForm.title, "Title should be null")
        assertNull(deserializedForm.subtitle, "Subtitle should be null")
        assertNull(deserializedForm.previewEndpoint, "Preview endpoint should be null")
        assertNull(deserializedForm.externalFormId, "External form ID should be null")
        assertNull(deserializedForm.smartActionId, "Smart action ID should be null")
        assertNull(deserializedForm.image, "Image should be null")
    }

    @Test
    fun chatMessageFormDataSerializationAndDeserialization() {
        // Given
        val formData = WebFormData(
            externalFormId = "form123",
            smartActionId = 1,
            uri = "https://test.com/form"
        )
        val originalData = WebFormResponse(
            type = "form",
            signature = "test-signature",
            data = formData
        )

        // When
        val jsonString = Gson().toJson(originalData)
        val deserializedData = Gson().fromJson(jsonString, WebFormResponse::class.java)

        // Then
        assertNotNull(jsonString, "JSON string should not be null")
        assertEquals(originalData.type, deserializedData.type, "Type should match")
        assertEquals(originalData.signature, deserializedData.signature, "Signature should match")
        assertNotNull(deserializedData.data, "Form data should not be null")
        assertEquals(formData.externalFormId, deserializedData.data?.externalFormId, "External form ID should match")
        assertEquals(formData.smartActionId, deserializedData.data?.smartActionId, "Smart action ID should match")
        assertEquals(formData.uri, deserializedData.data?.uri, "URI should match")
    }

    @Test
    fun chatMessageFormDataWithNullValues() {
        // Given
        val originalData = WebFormResponse()

        // When
        val jsonString = Gson().toJson(originalData)
        val deserializedData = Gson().fromJson(jsonString, WebFormResponse::class.java)

        // Then
        assertNotNull(jsonString, "JSON string should not be null")
        assertNull(deserializedData.type, "Type should be null")
        assertNull(deserializedData.signature, "Signature should be null")
        assertNull(deserializedData.data, "Form data should be null")
    }

    @Test
    fun formDataSerializationAndDeserialization() {
        // Given
        val originalData = WebFormData(
            externalFormId = "form123",
            smartActionId = 1,
            uri = "https://test.com/form"
        )

        // When
        val jsonString = Gson().toJson(originalData)
        val deserializedData = Gson().fromJson(jsonString, WebFormData::class.java)

        // Then
        assertNotNull(jsonString, "JSON string should not be null")
        assertEquals(originalData.externalFormId, deserializedData.externalFormId, "External form ID should match")
        assertEquals(originalData.smartActionId, deserializedData.smartActionId, "Smart action ID should match")
        assertEquals(originalData.uri, deserializedData.uri, "URI should match")
    }

    @Test
    fun formDataWithDefaultValues() {
        // Given
        val originalData = WebFormData(
            externalFormId = null,
            uri = null
        )

        // When
        val jsonString = Gson().toJson(originalData)
        val deserializedData = Gson().fromJson(jsonString, WebFormData::class.java)

        // Then
        assertNotNull(jsonString, "JSON string should not be null")
        assertNull(deserializedData.externalFormId, "External form ID should be null")
        assertEquals(0, deserializedData.smartActionId, "Smart action ID should be default")
        assertNull(deserializedData.uri, "URI should be null")
    }
} 
