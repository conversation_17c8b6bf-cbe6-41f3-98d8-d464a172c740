package com.ccaiplatform.ccaichat.model

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class FormTest {

    private val gson: Gson = GsonBuilder()
        .registerTypeAdapter(Form::class.java, FormDeserializer())
        .create()

    @Test
    fun deserializeWebForm() {
        val json = """
            {
                "name": "webForm",
                "title": "Web Title",
                "subtitle": "Web Subtitle",
                "preview_endpoint": "http://preview",
                "external_form_id": "ext123",
                "smart_action_id": 1,
                "image": "img.png"
            }
        """
        val form = gson.fromJson(json, Form::class.java)
        assertTrue(form is Form.WebForm)
        form as Form.WebForm
        assertEquals("webForm", form.name)
        assertEquals("Web Title", form.title)
        assertEquals("Web Subtitle", form.subtitle)
        assertEquals("http://preview", form.previewEndpoint)
        assertEquals("ext123", form.externalFormId)
        assertEquals(1, form.smartActionId)
        assertEquals("img.png", form.image)
        assertFalse(form.completed)
    }

    @Test
    fun deserializeCustomForm() {
        val json = """
            {
                "form_type": "custom",
                "id": 10,
                "name": "customForm",
                "title": "Custom Title",
                "subtitle": "Custom Subtitle",
                "smart_action_id": 2,
                "image": "custom.png",
                "header": "Header",
                "footer": "Footer"
            }
        """
        val form = gson.fromJson(json, Form::class.java)
        assertTrue(form is Form.CustomForm)
        form as Form.CustomForm
        assertEquals(10, form.id)
        assertEquals("customForm", form.name)
        assertEquals("Custom Title", form.title)
        assertEquals("Custom Subtitle", form.subtitle)
        assertEquals(2, form.smartActionId)
        assertEquals("custom.png", form.image)
        assertEquals("Header", form.header)
        assertEquals("Footer", form.footer)
        assertFalse(form.completed)
    }

    @Test
    fun completedPropertyIsMutable() {
        val form = Form.WebForm(name = "test")
        assertFalse(form.completed)
        form.completed = true
        assertTrue(form.completed)
    }
}
