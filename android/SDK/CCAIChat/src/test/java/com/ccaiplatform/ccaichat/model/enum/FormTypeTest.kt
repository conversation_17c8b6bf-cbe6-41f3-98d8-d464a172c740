package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class FormTypeTest {

    private val gson: Gson = GsonBuilder()
        .registerTypeAdapter(FormType::class.java, FormTypeAdapter())
        .create()

    @Test
    fun fromValueReturnsCorrectEnum() {
        assertEquals(FormType.Web, FormType.fromValue("web"))
        assertEquals(FormType.Custom, FormType.fromValue("custom"))
        // Unknown value should default to Web
        assertEquals(FormType.Web, FormType.fromValue("unknown"))
        assertEquals(FormType.Web, FormType.fromValue(""))
    }

    @Test
    fun serializeEnumToJson() {
        assertEquals("\"web\"", gson.toJson(FormType.Web))
        assertEquals("\"custom\"", gson.toJson(FormType.Custom))
    }

    @Test
    fun deserializeJsonToEnum() {
        assertEquals(FormType.Web, gson.fromJson("\"web\"", FormType::class.java))
        assertEquals(FormType.Custom, gson.fromJson("\"custom\"", FormType::class.java))
        // Unknown or empty should default to Web
        assertEquals(FormType.Web, gson.fromJson("\"unknown\"", FormType::class.java))
        assertEquals(FormType.Web, gson.fromJson("\"\"", FormType::class.java))
    }
}
