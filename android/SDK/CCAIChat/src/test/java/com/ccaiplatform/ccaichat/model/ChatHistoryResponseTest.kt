package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.ccaiplatform.ccaikit.models.response.communication.HumanAgent
import com.ccaiplatform.ccaikit.models.response.communication.VirtualAgent
import com.ccaiplatform.ccaikit.util.GsonProvider
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.util.Date

class ChatHistoryResponseTest {

    private val gson = GsonProvider.gson

    @Test
    fun chatHistoryResponseWithAllNullValues() {
        val response = ChatHistoryResponse()
        
        assertNull(response.chats)
        assertNull(response.pagination)
    }

    @Test
    fun chatHistoryResponseWithValidValues() {
        val pagination = Pagination(nextPage = 2, perPage = 10)
        val interaction = ChatInteraction(commType = "chat", commId = 123)
        val response = ChatHistoryResponse(
            chats = listOf(interaction),
            pagination = pagination
        )
        
        assertNotNull(response.chats)
        assertNotNull(response.pagination)
        assertEquals(1, response.chats?.size)
        assertEquals("chat", response.chats?.first()?.commType)
        assertEquals(2, response.pagination?.nextPage)
    }

    @Test
    fun chatHistoryResponseJsonSerialization() {
        val pagination = Pagination(nextPage = 3, perPage = 20)
        val interaction = ChatInteraction(
            commType = "live_chat",
            commId = 456,
            transcriptVersion = "v1.0",
            assignedAt = "2023-01-01T00:00:00Z",
            timezone = "UTC"
        )
        val response = ChatHistoryResponse(
            chats = listOf(interaction),
            pagination = pagination
        )
        
        val json = gson.toJson(response)
        
        assertTrue(json.contains("\"chats\""))
        assertTrue(json.contains("\"pagination\""))
        assertTrue(json.contains("\"comm_type\":\"live_chat\""))
        assertTrue(json.contains("\"next_page\":3"))
        assertTrue(json.contains("\"per_page\":20"))
    }

    @Test
    fun chatHistoryResponseJsonDeserialization() {
        val json = """
        {
            "chats": [
                {
                    "comm_type": "email",
                    "comm_id": 789,
                    "transcript_version": "v2.0",
                    "assigned_at": "2023-06-15T12:30:00Z",
                    "timezone": "America/New_York"
                }
            ],
            "pagination": {
                "next_page": 5,
                "per_page": 15
            }
        }
        """.trimIndent()
        
        val response = gson.fromJson(json, ChatHistoryResponse::class.java)
        
        assertNotNull(response.chats)
        assertNotNull(response.pagination)
        assertEquals(1, response.chats?.size)
        assertEquals("email", response.chats?.first()?.commType)
        assertEquals(789, response.chats?.first()?.commId)
        assertEquals("v2.0", response.chats?.first()?.transcriptVersion)
        assertEquals("2023-06-15T12:30:00Z", response.chats?.first()?.assignedAt)
        assertEquals("America/New_York", response.chats?.first()?.timezone)
        assertEquals(5, response.pagination?.nextPage)
        assertEquals(15, response.pagination?.perPage)
    }

    @Test
    fun chatHistoryResponseSetterMethods() {
        val response = ChatHistoryResponse()
        val chats = listOf(ChatInteraction())
        val pagination = Pagination()

        // Test setter methods
        response.chats = chats
        response.pagination = pagination

        assertEquals(chats, response.chats)
        assertEquals(pagination, response.pagination)
    }

    @Test
    fun paginationWithAllNullValues() {
        val pagination = Pagination()
        
        assertNull(pagination.nextPage)
        assertNull(pagination.perPage)
    }

    @Test
    fun paginationWithValidValues() {
        val pagination = Pagination(nextPage = 1, perPage = 50)
        
        assertEquals(1, pagination.nextPage)
        assertEquals(50, pagination.perPage)
    }

    @Test
    fun paginationJsonSerialization() {
        val pagination = Pagination(nextPage = 42, perPage = 100)
        
        val json = gson.toJson(pagination)
        
        assertTrue(json.contains("\"next_page\":42"))
        assertTrue(json.contains("\"per_page\":100"))
    }

    @Test
    fun paginationJsonDeserialization() {
        val json = """
        {
            "next_page": 7,
            "per_page": 25
        }
        """.trimIndent()
        
        val pagination = gson.fromJson(json, Pagination::class.java)
        
        assertEquals(7, pagination.nextPage)
        assertEquals(25, pagination.perPage)
    }

    @Test
    fun paginationSetterMethods() {
        val pagination = Pagination()

        // Test setter methods
        pagination.nextPage = 2
        pagination.perPage = 10

        assertEquals(2, pagination.nextPage)
        assertEquals(10, pagination.perPage)
    }

    @Test
    fun chatInteractionWithAllNullValues() {
        val interaction = ChatInteraction()
        
        assertNull(interaction.commType)
        assertNull(interaction.commId)
        assertNull(interaction.transcriptVersion)
        assertNull(interaction.assignedAt)
        assertNull(interaction.timezone)
        assertNull(interaction.entries)
    }

    @Test
    fun chatInteractionWithValidValues() {
        val entries = listOf(
            Entries(
                timestamp = 1234567890L,
                role = Role.END_USER,
                body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Hello")
            )
        )
        
        val interaction = ChatInteraction(
            commType = "webchat",
            commId = 999,
            transcriptVersion = "v3.0",
            assignedAt = "2023-12-01T10:00:00Z",
            timezone = "Europe/London",
            entries = entries
        )
        
        assertEquals("webchat", interaction.commType)
        assertEquals(999, interaction.commId)
        assertEquals("v3.0", interaction.transcriptVersion)
        assertEquals("2023-12-01T10:00:00Z", interaction.assignedAt)
        assertEquals("Europe/London", interaction.timezone)
        assertEquals(1, interaction.entries?.size)
    }

    @Test
    fun chatInteractionGetPreviousChatMessagesWithValidEntries() {
        val entries = listOf(
            Entries(
                timestamp = 1111111111L,
                role = Role.END_USER,
                body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "First message"),
                userData = UserData(name = "User1", id = 1)
            ),
            Entries(
                timestamp = 2222222222L,
                role = Role.AGENT,
                body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Reply message"),
                userData = UserData(name = "Agent1", id = 2)
            ),
            Entries(
                timestamp = 3333333333L,
                role = Role.VIRTUAL_AGENT,
                body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Bot response"),
                userData = UserData(name = "Bot", id = 3)
            )
        )
        
        val interaction = ChatInteraction(entries = entries)
        val messages = interaction.getPreviousChatMessages(123)
        
        assertEquals(3, messages.size)
        assertEquals("end_user-1", messages[0].author)
        assertEquals("user-2", messages[1].author)
        assertEquals("virtual_agent-3", messages[2].author)
        assertNotNull(messages[0].bodyRaw)
        assertNotNull(messages[1].bodyRaw)
        assertNotNull(messages[2].bodyRaw)
    }

    @Test
    fun chatInteractionGetPreviousChatMessagesWithNullEntries() {
        val interaction = ChatInteraction(entries = null)
        val messages = interaction.getPreviousChatMessages(123)
        
        assertTrue(messages.isEmpty())
    }

    @Test
    fun chatInteractionGetPreviousChatMessagesWithEmptyEntries() {
        val interaction = ChatInteraction(entries = emptyList())
        val messages = interaction.getPreviousChatMessages(123)
        
        assertTrue(messages.isEmpty())
    }

    @Test
    fun chatInteractionGetChatAgentsWithVirtualAgent() {
        val entries = listOf(
            Entries(
                role = Role.VIRTUAL_AGENT,
                userData = UserData(
                    name = "Virtual Assistant",
                    id = 100,
                    avatarUrl = "https://example.com/bot.jpg"
                )
            )
        )
        
        val interaction = ChatInteraction(entries = entries)
        val agents = interaction.getChatAgents()
        
        assertEquals(1, agents.size)
        assertTrue(agents[0] is VirtualAgent)
        val virtualAgent = agents[0] as VirtualAgent
        assertEquals(100, virtualAgent.id)
        assertEquals("Virtual Assistant", virtualAgent.name)
        assertEquals("https://example.com/bot.jpg", virtualAgent.avatarUrl)
    }

    @Test
    fun chatInteractionGetChatAgentsWithHumanAgent() {
        val entries = listOf(
            Entries(
                role = Role.AGENT,
                userData = UserData(
                    name = "John Doe",
                    id = 200,
                    avatarUrl = "https://example.com/john.jpg"
                )
            )
        )
        
        val interaction = ChatInteraction(entries = entries)
        val agents = interaction.getChatAgents()
        
        assertEquals(1, agents.size)
        assertTrue(agents[0] is HumanAgent)
        val humanAgent = agents[0] as HumanAgent
        assertEquals(200, humanAgent.id)
        assertEquals("John Doe", humanAgent.name)
        assertEquals("https://example.com/john.jpg", humanAgent.avatarUrl)
    }

    @Test
    fun chatInteractionGetChatAgentsWithMixedRoles() {
        val entries = listOf(
            Entries(
                role = Role.VIRTUAL_AGENT,
                userData = UserData(name = "Bot", id = 1, avatarUrl = "bot.jpg")
            ),
            Entries(
                role = Role.AGENT,
                userData = UserData(name = "Agent", id = 2, avatarUrl = "agent.jpg")
            ),
            Entries(
                role = Role.END_USER,
                userData = UserData(name = "User", id = 3, avatarUrl = "user.jpg")
            ),
            Entries(
                role = Role.SYSTEM,
                userData = UserData(name = "System", id = 4, avatarUrl = "system.jpg")
            )
        )
        
        val interaction = ChatInteraction(entries = entries)
        val agents = interaction.getChatAgents()
        
        // Only VIRTUAL_AGENT and AGENT should be included
        assertEquals(2, agents.size)
        assertTrue(agents[0] is VirtualAgent)
        assertTrue(agents[1] is HumanAgent)
    }

    @Test
    fun chatInteractionGetChatAgentsWithNullUserData() {
        val entries = listOf(
            Entries(role = Role.VIRTUAL_AGENT, userData = null),
            Entries(role = Role.AGENT, userData = null)
        )
        
        val interaction = ChatInteraction(entries = entries)
        val agents = interaction.getChatAgents()
        
        assertEquals(2, agents.size)
        assertTrue(agents[0] is VirtualAgent)
        assertTrue(agents[1] is HumanAgent)
        
        val virtualAgent = agents[0] as VirtualAgent
        assertNull(virtualAgent.id)
        assertNull(virtualAgent.name)
        assertNull(virtualAgent.avatarUrl)
        
        val humanAgent = agents[1] as HumanAgent
        assertNull(humanAgent.id)
        assertNull(humanAgent.name)
        assertNull(humanAgent.avatarUrl)
    }

    @Test
    fun chatInteractionGetChatAgentsWithPartialUserData() {
        val entries = listOf(
            Entries(
                role = Role.VIRTUAL_AGENT,
                userData = UserData(name = "Bot", id = null, avatarUrl = null)
            ),
            Entries(
                role = Role.AGENT,
                userData = UserData(name = null, id = 123, avatarUrl = "avatar.jpg")
            )
        )
        
        val interaction = ChatInteraction(entries = entries)
        val agents = interaction.getChatAgents()
        
        assertEquals(2, agents.size)
        
        val virtualAgent = agents[0] as VirtualAgent
        assertEquals("Bot", virtualAgent.name)
        assertNull(virtualAgent.id)
        assertNull(virtualAgent.avatarUrl)
        
        val humanAgent = agents[1] as HumanAgent
        assertNull(humanAgent.name)
        assertEquals(123, humanAgent.id)
        assertEquals("avatar.jpg", humanAgent.avatarUrl)
    }

    @Test
    fun chatInteractionGetChatAgentsWithNullEntries() {
        val interaction = ChatInteraction(entries = null)
        val agents = interaction.getChatAgents()
        
        assertTrue(agents.isEmpty())
    }

    @Test
    fun chatInteractionGetChatAgentsWithEmptyEntries() {
        val interaction = ChatInteraction(entries = emptyList())
        val agents = interaction.getChatAgents()
        
        assertTrue(agents.isEmpty())
    }

    @Test
    fun chatInteractionGetChatAgentsWithNoAgentRoles() {
        val entries = listOf(
            Entries(role = Role.END_USER, userData = UserData(name = "User", id = 1)),
            Entries(role = Role.SYSTEM, userData = UserData(name = "System", id = 2))
        )
        
        val interaction = ChatInteraction(entries = entries)
        val agents = interaction.getChatAgents()
        
        assertTrue(agents.isEmpty())
    }

    @Test
    fun chatInteractionJsonSerialization() {
        val entries = listOf(
            Entries(
                timestamp = 1234567890L,
                role = Role.END_USER,
                body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Test")
            )
        )
        
        val interaction = ChatInteraction(
            commType = "sms",
            commId = 777,
            transcriptVersion = "v1.5",
            assignedAt = "2023-07-20T14:30:00Z",
            timezone = "Asia/Tokyo",
            entries = entries
        )
        
        val json = gson.toJson(interaction)
        
        assertTrue(json.contains("\"comm_type\":\"sms\""))
        assertTrue(json.contains("\"comm_id\":777"))
        assertTrue(json.contains("\"transcript_version\":\"v1.5\""))
        assertTrue(json.contains("\"assigned_at\":\"2023-07-20T14:30:00Z\""))
        assertTrue(json.contains("\"timezone\":\"Asia/Tokyo\""))
        assertTrue(json.contains("\"entries\""))
    }

    @Test
    fun chatInteractionJsonDeserialization() {
        val json = """
        {
            "comm_type": "voice",
            "comm_id": 888,
            "transcript_version": "v2.1",
            "assigned_at": "2023-08-15T09:45:00Z",
            "timezone": "Australia/Sydney",
            "entries": [
                {
                    "timestamp": 9876543210,
                    "role": "end_user",
                    "body": {
                        "type": "text",
                        "content": "Hello from JSON"
                    }
                }
            ]
        }
        """.trimIndent()
        
        val interaction = gson.fromJson(json, ChatInteraction::class.java)
        
        assertEquals("voice", interaction.commType)
        assertEquals(888, interaction.commId)
        assertEquals("v2.1", interaction.transcriptVersion)
        assertEquals("2023-08-15T09:45:00Z", interaction.assignedAt)
        assertEquals("Australia/Sydney", interaction.timezone)
        assertNotNull(interaction.entries)
        assertEquals(1, interaction.entries?.size)
        assertEquals(9876543210L, interaction.entries?.first()?.timestamp)
        assertEquals(Role.END_USER, interaction.entries?.first()?.role)
    }

    @Test
    fun chatInteractionDataClassEquality() {
        val entries = listOf(
            Entries(timestamp = 1111L, role = Role.END_USER)
        )
        
        val interaction1 = ChatInteraction(
            commType = "test",
            commId = 123,
            transcriptVersion = "v1.0",
            assignedAt = "2023-01-01T00:00:00Z",
            timezone = "UTC",
            entries = entries
        )
        
        val interaction2 = ChatInteraction(
            commType = "test",
            commId = 123,
            transcriptVersion = "v1.0",
            assignedAt = "2023-01-01T00:00:00Z",
            timezone = "UTC",
            entries = entries
        )
        
        assertEquals(interaction1, interaction2)
        assertEquals(interaction1.hashCode(), interaction2.hashCode())
    }

    @Test
    fun chatInteractionDataClassInequality() {
        val interaction1 = ChatInteraction(commType = "chat", commId = 1)
        val interaction2 = ChatInteraction(commType = "chat", commId = 2)
        
        assertNotEquals(interaction1, interaction2)
    }

    @Test
    fun chatIntegrationTestCompleteFlow() {
        // Test the complete flow from ChatHistoryResponse to individual messages and agents
        val entries = listOf(
            Entries(
                timestamp = 1111111111L,
                role = Role.END_USER,
                body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "User message"),
                userData = UserData(name = "John", id = 1)
            ),
            Entries(
                timestamp = 2222222222L,
                role = Role.VIRTUAL_AGENT,
                body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Bot response"),
                userData = UserData(name = "Assistant", id = 2, avatarUrl = "bot.jpg")
            ),
            Entries(
                timestamp = 3333333333L,
                role = Role.AGENT,
                body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Agent reply"),
                userData = UserData(name = "Support", id = 3, avatarUrl = "agent.jpg")
            )
        )
        
        val interaction = ChatInteraction(
            commType = "webchat",
            commId = 999,
            entries = entries
        )
        
        val pagination = Pagination(nextPage = 2, perPage = 10)
        
        val response = ChatHistoryResponse(
            chats = listOf(interaction),
            pagination = pagination
        )
        
        // Test the full response structure
        assertNotNull(response.chats)
        assertEquals(1, response.chats?.size)
        assertNotNull(response.pagination)
        
        // Test getting previous messages
        val messages = interaction.getPreviousChatMessages(123)
        assertEquals(3, messages.size)
        assertEquals("end_user-1", messages[0].author)
        assertEquals("virtual_agent-2", messages[1].author)
        assertEquals("user-3", messages[2].author)
        
        // Test getting agents
        val agents = interaction.getChatAgents()
        assertEquals(2, agents.size) // Only VIRTUAL_AGENT and AGENT
        assertTrue(agents[0] is VirtualAgent)
        assertTrue(agents[1] is HumanAgent)
        
        val virtualAgent = agents[0] as VirtualAgent
        assertEquals("Assistant", virtualAgent.name)
        assertEquals("bot.jpg", virtualAgent.avatarUrl)
        
        val humanAgent = agents[1] as HumanAgent
        assertEquals("Support", humanAgent.name)
        assertEquals("agent.jpg", humanAgent.avatarUrl)
    }

    @Test
    fun entryWithAllNullValues() {
        val entries = Entries()

        assertNull(entries.timestamp)
        assertNull(entries.body)
        assertNull(entries.role)
        assertNull(entries.userData)
    }

    @Test
    fun entryWithValidValues() {
        val timestamp = System.currentTimeMillis()
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Hello")
        val role = Role.END_USER
        val userData = UserData(name = "Test User", id = 123, avatarUrl = "avatar.jpg")

        val entries = Entries(
            timestamp = timestamp,
            body = body,
            role = role,
            userData = userData
        )

        assertEquals(timestamp, entries.timestamp)
        assertEquals(body, entries.body)
        assertEquals(role, entries.role)
        assertEquals(userData, entries.userData)
    }

    @Test
    fun entryChatMessageWithValidTimestamp() {
        val timestamp = 1234567890L
        val entries = Entries(
            timestamp = timestamp,
            body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Test message"),
            role = Role.END_USER,
            userData = null
        )

        val chatMessage = entries.toChatMessage(123)

        assertEquals(timestamp, chatMessage.index)
        assertEquals(Date(timestamp), chatMessage.date)
        assertEquals("end_user-123", chatMessage.author)
        assertNotNull(chatMessage.bodyRaw)
    }

    @Test
    fun entryToChatMessageWithNullTimestamp() {
        val entries = Entries(
            timestamp = null,
            body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Test message"),
            role = Role.END_USER,
            userData = null
        )

        val chatMessage = entries.toChatMessage(123)

        assertNull(chatMessage.index)
        assertNotNull(chatMessage.date) // Should be current date
        assertEquals("end_user-123", chatMessage.author)
    }

    @Test
    fun entryGetAuthorWithEndUserRole() {
        val entries = Entries(
            role = Role.END_USER,
            userData = null
        )

        val chatMessage = entries.toChatMessage(456)

        assertEquals("end_user-456", chatMessage.author)
    }

    @Test
    fun entryGetAuthorWithAgentRole() {
        val entries = Entries(
            role = Role.AGENT,
            userData = null
        )

        val chatMessage = entries.toChatMessage(123)

        assertEquals("user", chatMessage.author)
    }

    @Test
    fun entryGetAuthorWithVirtualAgentRole() {
        val entries = Entries(
            role = Role.VIRTUAL_AGENT,
            userData = null
        )

        val chatMessage = entries.toChatMessage(123)

        assertEquals("virtual_agent", chatMessage.author)
    }

    @Test
    fun entryGetAuthorWithSystemRole() {
        val entries = Entries(
            role = Role.SYSTEM,
            userData = null
        )

        val chatMessage = entries.toChatMessage(123)

        assertEquals("system", chatMessage.author)
    }

    @Test
    fun entryGetAuthorWithUserDataId() {
        val userData = UserData(name = "Test User", id = 789, avatarUrl = "avatar.jpg")
        val entries = Entries(
            role = Role.END_USER,
            userData = userData
        )

        val chatMessage = entries.toChatMessage(123)

        assertEquals("end_user-789", chatMessage.author)
    }

    @Test
    fun entryGetAuthorWithAgentRoleAndUserDataId() {
        val userData = UserData(name = "Agent User", id = 999, avatarUrl = "avatar.jpg")
        val entries = Entries(
            role = Role.AGENT,
            userData = userData
        )

        val chatMessage = entries.toChatMessage(123)

        assertEquals("user-999", chatMessage.author)
    }

    @Test
    fun entryGetAuthorWithNullRole() {
        val entries = Entries(
            role = null,
            userData = null
        )

        val chatMessage = entries.toChatMessage(123)

        assertEquals("system", chatMessage.author) // Should default to SYSTEM
    }

    @Test
    fun entryGetAuthorWithNullEndUserId() {
        val entries = Entries(
            role = Role.END_USER,
            userData = null
        )

        val chatMessage = entries.toChatMessage(null)

        assertEquals("end_user", chatMessage.author)
    }

    @Test
    fun entryGetBodyWithNormalContent() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Hello World")
        val entries = Entries(
            role = Role.END_USER,
            body = body
        )

        val chatMessage = entries.toChatMessage(123)

        assertNotNull(chatMessage.bodyRaw)
        assertTrue(chatMessage.bodyRaw!!.contains("Hello World"))
    }

    @Test
    fun entryGetBodyWithEndUserPhoto() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Photo)
        val entries = Entries(
            role = Role.END_USER,
            body = body
        )

        val chatMessage = entries.toChatMessage(123)

        assertNotNull(chatMessage.bodyRaw)
        assertTrue(chatMessage.bodyRaw!!.contains("endUserSentPhoto"))
        assertTrue(chatMessage.bodyRaw!!.contains("\"type\":\"noti\""))
    }

    @Test
    fun entryGetBodyWithEndUserVideo() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Video)
        val entries = Entries(
            role = Role.END_USER,
            body = body
        )

        val chatMessage = entries.toChatMessage(123)

        assertNotNull(chatMessage.bodyRaw)
        assertTrue(chatMessage.bodyRaw!!.contains("endUserSentVideo"))
        assertTrue(chatMessage.bodyRaw!!.contains("\"type\":\"noti\""))
    }

    @Test
    fun entryGetBodyWithAgentPhoto() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Photo)
        val entries = Entries(
            role = Role.AGENT,
            body = body
        )

        val chatMessage = entries.toChatMessage(123)

        assertNotNull(chatMessage.bodyRaw)
        // Should not be converted to notification for agent
        assertFalse(chatMessage.bodyRaw!!.contains("endUserSentPhoto"))
    }

    @Test
    fun entryGetBodyWithNullBody() {
        val entries = Entries(
            role = Role.END_USER,
            body = null
        )

        val chatMessage = entries.toChatMessage(123)

        assertNull(chatMessage.bodyRaw)
    }

    @Test
    fun entryJsonSerialization() {
        val timestamp = 1234567890L
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Test message")
        val role = Role.END_USER
        val userData = UserData(name = "Test User", id = 123, avatarUrl = "avatar.jpg")

        val entries = Entries(
            timestamp = timestamp,
            body = body,
            role = role,
            userData = userData
        )

        val json = gson.toJson(entries)

        assertTrue(json.contains("\"timestamp\":$timestamp"))
        assertTrue(json.contains("\"role\":\"end_user\""))
        assertTrue(json.contains("\"name\":\"Test User\""))
    }

    @Test
    fun entryJsonDeserialization() {
        val json = """
        {
            "timestamp": 1234567890,
            "body": {
                "type": "text",
                "content": "Test message"
            },
            "role": "end_user",
            "user_data": {
                "name": "Test User",
                "id": 123,
                "avatar_url": "avatar.jpg"
            }
        }
        """.trimIndent()

        val entries = gson.fromJson(json, Entries::class.java)

        assertEquals(1234567890L, entries.timestamp)
        assertEquals(ChatMessageBodyType.Text, entries.body?.type)
        assertEquals("Test message", entries.body?.content)
        assertEquals(Role.END_USER, entries.role)
        assertEquals("Test User", entries.userData?.name)
        assertEquals(123, entries.userData?.id)
        assertEquals("avatar.jpg", entries.userData?.avatarUrl)
    }

    @Test
    fun entrySetterMethods() {
        val entries = Entries()
        val timestamp = 1234567890L
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Test")
        val role = Role.END_USER
        val userData = UserData(name = "Test", id = 123)

        // Test setter methods
        entries.timestamp = timestamp
        entries.body = body
        entries.role = role
        entries.userData = userData

        assertEquals(timestamp, entries.timestamp)
        assertEquals(body, entries.body)
        assertEquals(role, entries.role)
        assertEquals(userData, entries.userData)
    }
}
