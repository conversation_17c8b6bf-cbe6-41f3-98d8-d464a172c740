package com.ccaiplatform.ccaichat.interfaces

import com.ccaiplatform.ccaichat.model.PlainTextChatMessageRequest
import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.verify

class ChatProviderInterfaceTest {

    @Test
    fun join() = runBlocking {
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        provider.join()
        verify(provider).join()
    }

    @Test
    fun leave() = runBlocking {
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        provider.leave()
        verify(provider).leave()
    }

    @Test
    fun setDelegate() {
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        val listener = Mockito.mock(ChatProviderListener::class.java)
        provider.setListener(listener)
        verify(provider).setListener(listener)
    }

    @Test
    fun setDelegateWithNull() {
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        provider.setListener(null)
        verify(provider).setListener(null)
    }

    @Test
    fun sendMessage() = runBlocking {
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        val message = PlainTextChatMessageRequest(type = ChatMessageBodyType.Text, content = "Hello")
        provider.sendMessage(message)
        verify(provider).sendMessage(message)
    }

    @Test
    fun sendMessagePreview() {
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        val preview = "Hello"
        provider.sendMessagePreview(preview)
        verify(provider).sendMessagePreview(preview)
    }

    @Test
    fun typing() {
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        provider.typing(true)
        verify(provider).typing(true)
        provider.typing(false)
        verify(provider).typing(false)
    }
}
