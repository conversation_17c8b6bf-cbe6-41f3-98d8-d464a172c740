package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.DocumentType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class AssetTest {

    @Test
    fun shouldCreateAsset() {
        val asset = Asset(
            url = "http://example.com/document.pdf",
            text = "Document Text",
            fileName = "document.pdf"
        )
        assertEquals("http://example.com/document.pdf", asset.url)
        assertEquals("Document Text", asset.text)
        assertEquals("document.pdf", asset.getFileName({ "document2.pdf" }))
    }

    @Test
    fun shouldCreateAssetWithNullFields() {
        val asset = Asset(
            url = null,
            text = null,
            fileName = null
        )
        assertEquals(null, asset.url)
        assertEquals(null, asset.text)
    }

    @Test
    fun shouldGuessFileNameFromUrl() {
        val asset = Asset(
            url = "http://example.com/document.pdf",
            text = "Document Text",
            fileName = null
        )
        assertEquals("document.pdf", asset.getFileName({ "document.pdf" }))
    }

    @Test
    fun getFileNameReturnsFileNameIfPresent() {
        val asset = Asset(url = "http://example.com/file.pdf", text = "desc", fileName = "custom.pdf")
        assertEquals("custom.pdf", asset.getFileName { "guessed.pdf" })
    }

    @Test
    fun getFileNameGuessesFileNameIfAbsent() {
        val asset = Asset(url = "http://example.com/file.docx", text = "desc", fileName = null)
        val guessed = asset.getFileName { "guessed.docx" }
        assertEquals("guessed.docx", guessed)
    }

    @Test
    fun documentTypePdf() {
        val asset = Asset(url = "http://example.com/file.pdf", fileName = "file.pdf")
        assertEquals(DocumentType.PDF, asset.documentType)
    }

    @Test
    fun documentTypeExcel() {
        val asset = Asset(url = "http://example.com/file.xlsx", fileName = "file.xlsx")
        assertEquals(DocumentType.EXCEL, asset.documentType)
    }

    @Test
    fun documentTypeDoc() {
        val asset = Asset(url = "http://example.com/file.doc", fileName = "file.doc")
        assertEquals(DocumentType.DOC, asset.documentType)
    }

    @Test
    fun documentTypePpt() {
        val asset = Asset(url = "http://example.com/file.pptx", fileName = "file.pptx")
        assertEquals(DocumentType.PPT, asset.documentType)
    }

    @Test
    fun documentTypeAudio() {
        val asset = Asset(url = "http://example.com/file.mp3", fileName = "file.mp3")
        assertEquals(DocumentType.AUDIO, asset.documentType)
    }

    @Test
    fun documentTypeVideo() {
        val asset = Asset(url = "http://example.com/file.mp4", fileName = "file.mp4")
        assertEquals(DocumentType.VIDEO, asset.documentType)
    }

    @Test
    fun documentTypeGeneric() {
        val asset = Asset(url = "http://example.com/file.unknown", fileName = "file.unknown")
        assertEquals(DocumentType.GENERIC, asset.documentType)
    }
}
