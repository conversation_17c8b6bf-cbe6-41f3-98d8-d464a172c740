package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.ccaiplatform.ccaikit.util.GsonProvider
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.util.Date

class ChatMessageTest {

    @Test
    fun initialization() {
        val date = Date()
        val message = ChatMessage(
            index = 1,
            date = date,
            author = "test-author",
            bodyRaw = "Hello World"
        )

        assertEquals(1L, message.index)
        assertEquals(date, message.date)
        assertEquals("test-author", message.author)
        assertEquals("Hello World", message.bodyRaw)
        assertEquals(ChatMessageBodyType.Unknown, message.body.type)
        assertNull(message.body.content)
    }

    @Test
    fun initializationWithNullValues() {
        val message = ChatMessage(
            index = null,
            date = Date(),
            author = null,
            bodyRaw = null
        )

        assertNull(message.index)
        assertTrue(message.date.time > 0)
        assertNull(message.author)
        assertNull(message.bodyRaw)
        assertEquals(ChatMessageBodyType.Unknown, message.body.type)
        assertNull(message.body.content)
    }

    @Test
    fun authorSanitization() {
        val message1 = ChatMessage(
            index = 1,
            date = Date(),
            author = "test-author",
            bodyRaw = "Hello"
        )
        assertEquals("test-author", message1.author)

        val message2 = ChatMessage(
            index = 2,
            date = Date(),
            author = "simple-name",
            bodyRaw = "Hello"
        )
        assertEquals("simple-name", message2.author)
    }

    @Test
    fun decodingBodyFromJSON() {
        val validJsonBody = """
        {
            "type": "text",
            "content": "Hello World"
        }
        """.trimIndent()

        val message1 = ChatMessage(
            index = 1,
            date = Date(),
            author = "test",
            bodyRaw = validJsonBody
        )
        assertEquals(ChatMessageBodyType.Text, message1.body.type)
        assertEquals("Hello World", message1.body.content)
    }

    @Test
    fun decodingBodyFromInvalidJSON() {
        val invalidJsonBody = "Invalid JSON"
        val message2 = ChatMessage(
            index = 2,
            date = Date(),
            author = "test",
            bodyRaw = invalidJsonBody
        )
        assertEquals(ChatMessageBodyType.Unknown, message2.body.type)
        assertNull(message2.body.content)

        val message3 = ChatMessage(
            index = 3,
            date = Date(),
            author = "test",
            bodyRaw = ""
        )
        assertEquals(ChatMessageBodyType.Unknown, message3.body.type)
        assertNull(message3.body.content)
    }

    @Test
    fun decodingTextMessageFromJSON() {
        val textBody = """
        {
            "type": "text",
            "content": "Hello World"
        }
        """.trimIndent()

        val textMessage = ChatMessage(
            index = 1,
            date = Date(),
            author = "test",
            bodyRaw = textBody
        )
        assertEquals(ChatMessageBodyType.Text, textMessage.body.type)
        assertEquals("Hello World", textMessage.body.content)
    }

    @Test
    fun decodingImageMessageFromJSON() {
        val imageBody = """
        {
            "type": "image",
            "content": "https://ilabs.ujetdemo.co/assets2/default-virtual-agent-avatar-2.png"
        }
        """.trimIndent()

        val imageMessage = ChatMessage(
            index = 2,
            date = Date(),
            author = "test",
            bodyRaw = imageBody
        )
        assertEquals(ChatMessageBodyType.Image, imageMessage.body.type)
        assertEquals(
            "https://ilabs.ujetdemo.co/assets2/default-virtual-agent-avatar-2.png",
            imageMessage.body.content
        )
    }

    @Test
    fun decodingUnknownMessageFromJSON() {
        val unknownBody = """
        {
            "type": "unknown",
            "content": "Some content"
        }
        """.trimIndent()

        val unknownMessage = ChatMessage(
            index = 3,
            date = Date(),
            author = "test",
            bodyRaw = unknownBody
        )
        assertEquals(ChatMessageBodyType.Unknown, unknownMessage.body.type)
        assertEquals("Some content", unknownMessage.body.content)
    }

    @Test
    fun serializationDeserializationId() {
        val now = Date()
        val bodyRawJson = """
            {
                "type": "text",
                "content": "Test"
            }
        """.trimIndent()
        val message = ChatMessage(
            index = 1,
            date = now,
            author = "author",
            bodyRaw = bodyRawJson,
            id = 123,
            participantId = null,
            taskVaId = null
        )
        val json = GsonProvider.gson.toJson(message)
        val decoded = GsonProvider.gson.fromJson(json, ChatMessage::class.java)
        assertEquals(123, decoded.id)
    }

    @Test
    fun serializationDeserializationParticipantId() {
        val now = Date()
        val bodyRawJson = """
            {
                "type": "text",
                "content": "Test"
            }
        """.trimIndent()
        val message = ChatMessage(
            index = 1,
            date = now,
            author = "author",
            bodyRaw = bodyRawJson,
            id = null,
            participantId = 456,
            taskVaId = null
        )
        val json = GsonProvider.gson.toJson(message)
        val decoded = GsonProvider.gson.fromJson(json, ChatMessage::class.java)
        assertEquals(456, decoded.participantId)
    }

    @Test
    fun serializationDeserializationTaskVaId() {
        val now = Date()
        val bodyRawJson = """
            {
                "type": "text",
                "content": "Test"
            }
        """.trimIndent()
        val message = ChatMessage(
            index = 1,
            date = now,
            author = "author",
            bodyRaw = bodyRawJson,
            id = null,
            participantId = null,
            taskVaId = 789
        )
        val json = GsonProvider.gson.toJson(message)
        val decoded = GsonProvider.gson.fromJson(json, ChatMessage::class.java)
        assertEquals(789, decoded.taskVaId)
    }

    @Test
    fun serializationDeserializationBodyContent() {
        val now = Date()
        val bodyRawJson = """
            {
                "type": "text",
                "content": "Full Test"
            }
        """.trimIndent()
        val message = ChatMessage(
            index = 99,
            date = now,
            author = "full-author",
            bodyRaw = bodyRawJson,
            id = 123,
            participantId = 456,
            taskVaId = 789
        )
        val json = GsonProvider.gson.toJson(message)
        val decoded = GsonProvider.gson.fromJson(json, ChatMessage::class.java)
        assertEquals("Full Test", decoded.body.content)
    }

    @Test
    fun idParticipantIdTaskVaIdDefaults() {
        val message = ChatMessage(
            index = 1,
            date = Date(),
            author = "test",
            bodyRaw = null
        )
        assertNull(message.id)
        assertNull(message.participantId)
        assertNull(message.taskVaId)
    }
}
