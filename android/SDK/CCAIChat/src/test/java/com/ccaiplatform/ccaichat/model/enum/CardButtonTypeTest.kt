package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class CardButtonTypeTest {

    private val gson: Gson = GsonBuilder()
        .registerTypeAdapter(CardButtonType::class.java, CardButtonTypeAdapter())
        .create()

    @Test
    fun fromValueReturnsCorrectEnum() {
        assertEquals(CardButtonType.Primary, CardButtonType.fromValue("primary"))
        assertEquals(CardButtonType.Secondary, CardButtonType.fromValue("secondary"))
        assertEquals(CardButtonType.None, CardButtonType.fromValue(""))
        assertEquals(CardButtonType.None, CardButtonType.fromValue("unknown"))
    }

    @Test
    fun serializeEnumToJson() {
        assertEquals("\"primary\"", gson.toJson(CardButtonType.Primary))
        assertEquals("\"secondary\"", gson.toJson(CardButtonType.Secondary))
        assertEquals("\"\"", gson.toJson(CardButtonType.None))
    }

    @Test
    fun deserializeJsonToEnum() {
        assertEquals(CardButtonType.Primary, gson.fromJson("\"primary\"", CardButtonType::class.java))
        assertEquals(CardButtonType.Secondary, gson.fromJson("\"secondary\"", CardButtonType::class.java))
        assertEquals(CardButtonType.None, gson.fromJson("\"\"", CardButtonType::class.java))
        assertEquals(CardButtonType.None, gson.fromJson("\"unknown\"", CardButtonType::class.java))
    }
}
