package com.ccaiplatform.ccaichat.interfaces

import com.ccaiplatform.ccaichat.model.ChatMessage
import com.ccaiplatform.ccaichat.model.enum.ChatMemberEvent
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichat.model.enum.ChatTypingEvent
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.verify
import java.util.Date

class ChatProviderListenerTest {

    @Test
    fun chatProviderMessagesReceived() {
        val listener = Mockito.mock(ChatProviderListener::class.java)
        val messages = listOf(
            ChatMessage(
            index = 1L,
            date = Date(),
            author = "user",
            bodyRaw = "{\"type\":\"text\",\"content\":\"Hello\"}"
            )
        )
        listener.onMessagesReceived(messages)
        verify(listener).onMessagesReceived(messages)
    }

    @Test
    fun chatProviderTypingEvent() {
        val listener = Mockito.mock(ChatProviderListener::class.java)
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        val typingEvent = ChatTypingEvent.Started("user")
        listener.onTypingEvent(typingEvent)
        verify(listener).onTypingEvent(typingEvent)
    }

    @Test
    fun chatProviderMemberEvent() {
        val listener = Mockito.mock(ChatProviderListener::class.java)
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        val memberEvent = ChatMemberEvent.Joined("user")
        listener.onMemberEvent(memberEvent)
        verify(listener).onMemberEvent(memberEvent)
    }

    @Test
    fun chatProviderStateChanged() {
        val listener = Mockito.mock(ChatProviderListener::class.java)
        val provider = Mockito.mock(ChatProviderInterface::class.java)
        val state = ChatProviderState.Connected
        listener.onStateChanged(state)
        verify(listener).onStateChanged(state)
    }

    @Test
    fun chatProviderNullProvider() {
        val listener = Mockito.mock(ChatProviderListener::class.java)
        val messages = listOf(
            ChatMessage(
            index = 1L,
            date = Date(),
            author = "user",
            bodyRaw = "{\"type\":\"text\",\"content\":\"Hello\"}"
            )
        )
        listener.onMessagesReceived(messages)
        verify(listener).onMessagesReceived(messages)
    }
}
