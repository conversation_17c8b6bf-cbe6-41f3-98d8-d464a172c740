package com.ccaiplatform.ccaichat.model

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class WebFormRequestTest {

    @Test
    fun constructorWithAllParameters() {
        // Given
        val signature = "test_signature"
        val smartActionId = 123
        val externalFormId = "form_456"
        val type = "custom_type"

        // When
        val request = WebFormRequest(
            signature = signature,
            smartActionId = smartActionId,
            externalFormId = externalFormId,
            type = type
        )

        // Then
        assertEquals(signature, request.signature)
        assertEquals(smartActionId, request.smartActionId)
        assertEquals(externalFormId, request.externalFormId)
        assertEquals(type, request.type)
    }

    @Test
    fun constructorWithDefaultType() {
        // Given
        val signature = "test_signature"
        val smartActionId = 123
        val externalFormId = "form_456"
        val expectedDefaultType = "form_message_received"

        // When
        val request = WebFormRequest(
            signature = signature,
            smartActionId = smartActionId,
            externalFormId = externalFormId
        )

        // Then
        assertEquals(signature, request.signature)
        assertEquals(smartActionId, request.smartActionId)
        assertEquals(externalFormId, request.externalFormId)
        assertEquals(expectedDefaultType, request.type)
    }

    @Test
    fun dataClassEqualsAndHashCode() {
        // Given
        val request1 = WebFormRequest(
            signature = "test_signature",
            smartActionId = 123,
            externalFormId = "form_456"
        )
        val request2 = WebFormRequest(
            signature = "test_signature",
            smartActionId = 123,
            externalFormId = "form_456"
        )
        val request3 = WebFormRequest(
            signature = "different_signature",
            smartActionId = 123,
            externalFormId = "form_456"
        )

        // Then
        assertTrue(request1 == request2, "Equal objects should be equal")
        assertTrue(request1.hashCode() == request2.hashCode(), "Equal objects should have same hashCode")
        assertFalse(request1 == request3, "Different objects should not be equal")
        assertFalse(request1.hashCode() == request3.hashCode(), "Different objects should have different hashCode")
    }

    @Test
    fun dataClassToString() {
        // Given
        val request = WebFormRequest(
            signature = "test_signature",
            smartActionId = 123,
            externalFormId = "form_456"
        )

        // When
        val toString = request.toString()

        // Then
        assertTrue(toString.contains("signature=test_signature"), "toString should contain signature")
        assertTrue(toString.contains("smartActionId=123"), "toString should contain smartActionId")
        assertTrue(toString.contains("externalFormId=form_456"), "toString should contain externalFormId")
        assertTrue(toString.contains("type=form_message_received"), "toString should contain type")
    }
} 
