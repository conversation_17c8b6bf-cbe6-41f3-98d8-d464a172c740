package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.CustomFormQuestionType
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class CustomFormTest {

    private val gson: Gson = GsonBuilder().create()

    @Test
    fun customFormRequestDefaults() {
        val req = CustomFormRequest("sig", 2, "extid")
        assertEquals("sig", req.signature)
        assertEquals(2, req.smartActionId)
        assertEquals("extid", req.externalFormId)
        assertEquals("form_message_received", req.type)
    }

    @Test
    fun customFormDetailsResponseToString() {
        val details = CustomFormDetailsResponse("title", "header", "footer", null)
        assertTrue(details.toString().contains("title=title"))
    }

    @Test
    fun customFormQuestionConstruction() {
        val option = CustomFormQuestionOption(1, "val", "img", 0)
        val question = CustomFormQuestion(
            id = 10,
            type = CustomFormQuestionType.TextEntry,
            isMandatory = true,
            isMasked = false,
            position = 1,
            question = "Q?",
            isMultiselect = false,
            placeholder = "ph",
            contentType = "ct",
            characterLimit = 100,
            helpText = "help",
            toggleOnText = "on",
            toggleOffText = "off",
            options = listOf(option)
        )
        assertEquals(10, question.id)
        assertEquals(CustomFormQuestionType.TextEntry, question.type)
        assertEquals(listOf(option), question.options)
    }

    @Test
    fun customFormQuestionOptionEquality() {
        val opt1 = CustomFormQuestionOption(1, "v", "i", 2)
        val opt2 = CustomFormQuestionOption(1, "v", "i", 2)
        assertEquals(opt1, opt2)
    }

    @Test
    fun submitCustomFormRequestAndAnswer() {
        val answer = SubmitCustomFormRequest.Answer(5, "val")
        val req = SubmitCustomFormRequest(7, listOf(answer))
        assertEquals(7, req.smartActionId)
        assertEquals(listOf(answer), req.formResponse)
    }

    @Test
    fun gsonSerializationDeserialization() {
        val data = CustomFormData("fid", 3, "uri")
        val json = gson.toJson(data)
        val fromJson = gson.fromJson(json, CustomFormData::class.java)
        assertEquals(data, fromJson)
    }
}
