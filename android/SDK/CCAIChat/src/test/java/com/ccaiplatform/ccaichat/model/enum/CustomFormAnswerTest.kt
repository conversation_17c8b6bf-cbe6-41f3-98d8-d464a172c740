package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.text.SimpleDateFormat

class CustomFormAnswerTest {

    @Test
    fun textReturnsTrimmedContentAndIsValidIfNotBlank() {
        val answer = CustomFormAnswer.Text("  hello  ")
        assertEquals("hello", answer.stringValue)
        assertTrue(answer.isValid)

        val blank = CustomFormAnswer.Text("   ")
        assertEquals("", blank.stringValue)
        assertFalse(blank.isValid)
    }

    @Test
    fun optionReturnsContentAndIsValidIfNotBlank() {
        val answer = CustomFormAnswer.Option(listOf(1, 2), "option content")
        assertEquals("option content", answer.stringValue)
        assertTrue(answer.isValid)

        val blank = CustomFormAnswer.Option(emptyList(), "   ")
        assertEquals("   ", blank.stringValue)
        assertFalse(blank.isValid)
    }

    @Test
    fun dateReturnsFormattedStringAndIsValid() {
        val date = SimpleDateFormat("yyyy-MM-dd").parse("2024-06-01")!!
        val answer = CustomFormAnswer.Date(date)
        assertEquals("06/01/2024", answer.stringValue)
        assertTrue(answer.isValid)
    }

    @Test
    fun timeReturnsFormattedStringAndIsValid() {
        val date = SimpleDateFormat("HH:mm").parse("13:45")!!
        val answer = CustomFormAnswer.Time(date)
        assertEquals("13:45", answer.stringValue)
        assertTrue(answer.isValid)
    }

    @Test
    fun toggleReturnsTrueOrFalseAsStringAndIsValid() {
        val on = CustomFormAnswer.Toggle(true)
        val off = CustomFormAnswer.Toggle(false)
        assertEquals("true", on.stringValue)
        assertEquals("false", off.stringValue)
        assertTrue(on.isValid)
        assertTrue(off.isValid)
    }

    @Test
    fun noneReturnsNullAndIsNotValid() {
        val none = CustomFormAnswer.None
        assertNull(none.stringValue)
        assertFalse(none.isValid)
    }
}
