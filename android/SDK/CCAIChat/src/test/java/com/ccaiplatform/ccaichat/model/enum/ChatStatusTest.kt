package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ChatStatusTest {

    @Test
    fun statusValues() {
        assertEquals("queued", ChatStatus.Queued.rawValue)
        assertEquals("assigned", ChatStatus.Assigned.rawValue)
        assertEquals("dismissed", ChatStatus.Dismissed.rawValue)
        assertEquals("finished", ChatStatus.Finished.rawValue)
        assertEquals("canceled", ChatStatus.Canceled.rawValue)
        assertEquals("failed", ChatStatus.Failed.rawValue)
        assertEquals("va_assigned", ChatStatus.VaAssigned.rawValue)
        assertEquals("va_dismissed", ChatStatus.VaDismissed.rawValue)
    }

    @Test
    fun unknownStatus() {
        val unknownValue = "unknown_status"
        val unknown = ChatStatus.Unknown(unknownValue)
        assertEquals(unknownValue, unknown.rawValue)
        assertEquals(unknownValue, unknown.value)
    }

    @Test
    fun fromRawValue() {
        assertEquals(ChatStatus.Queued, ChatStatus.fromRawValue("queued"))
        assertEquals(ChatStatus.Assigned, ChatStatus.fromRawValue("assigned"))
        assertEquals(ChatStatus.Dismissed, ChatStatus.fromRawValue("dismissed"))
        assertEquals(ChatStatus.Finished, ChatStatus.fromRawValue("finished"))
        assertEquals(ChatStatus.Canceled, ChatStatus.fromRawValue("canceled"))
        assertEquals(ChatStatus.Failed, ChatStatus.fromRawValue("failed"))
        assertEquals(ChatStatus.VaAssigned, ChatStatus.fromRawValue("va_assigned"))
        assertEquals(ChatStatus.VaDismissed, ChatStatus.fromRawValue("va_dismissed"))

        // Test unknown values
        val unknownValue = "unknown_status"
        val unknown = ChatStatus.fromRawValue(unknownValue)
        assertTrue(unknown is ChatStatus.Unknown)
        assertEquals(unknownValue, unknown.rawValue)

        // Test null and empty values
        val emptyUnknown = ChatStatus.fromRawValue("")
        assertTrue(emptyUnknown is ChatStatus.Unknown)
        assertEquals("invalid_empty", emptyUnknown.rawValue)

        val nullUnknown = ChatStatus.fromRawValue(null)
        assertTrue(nullUnknown is ChatStatus.Unknown)
        assertEquals("invalid_empty", nullUnknown.rawValue)
    }

    @Test
    fun equality() {
        // Test object equality
        assertEquals(ChatStatus.Queued, ChatStatus.Queued)
        assertEquals(ChatStatus.Assigned, ChatStatus.Assigned)
        assertEquals(ChatStatus.Dismissed, ChatStatus.Dismissed)
        assertEquals(ChatStatus.Finished, ChatStatus.Finished)
        assertEquals(ChatStatus.Canceled, ChatStatus.Canceled)
        assertEquals(ChatStatus.Failed, ChatStatus.Failed)
        assertEquals(ChatStatus.VaAssigned, ChatStatus.VaAssigned)
        assertEquals(ChatStatus.VaDismissed, ChatStatus.VaDismissed)

        // Test Unknown equality
        val unknown1 = ChatStatus.Unknown("test")
        val unknown2 = ChatStatus.Unknown("test")
        val unknown3 = ChatStatus.Unknown("different")
        assertEquals(unknown1, unknown2)
        assertFalse(unknown1 == unknown3)
    }

    @Test
    fun statusLists() {
        // Test validStatus list
        assertTrue(ChatStatus.validStatus.contains(ChatStatus.Queued))
        assertTrue(ChatStatus.validStatus.contains(ChatStatus.Assigned))
        assertTrue(ChatStatus.validStatus.contains(ChatStatus.Dismissed))
        assertTrue(ChatStatus.validStatus.contains(ChatStatus.Finished))
        assertTrue(ChatStatus.validStatus.contains(ChatStatus.Canceled))
        assertTrue(ChatStatus.validStatus.contains(ChatStatus.Failed))
        assertTrue(ChatStatus.validStatus.contains(ChatStatus.VaAssigned))
        assertTrue(ChatStatus.validStatus.contains(ChatStatus.VaDismissed))

        // Test inProgressStatus list
        assertTrue(ChatStatus.inProgressStatus.contains(ChatStatus.Queued))
        assertTrue(ChatStatus.inProgressStatus.contains(ChatStatus.Assigned))
        assertTrue(ChatStatus.inProgressStatus.contains(ChatStatus.VaAssigned))
        assertTrue(ChatStatus.inProgressStatus.contains(ChatStatus.Dismissed))
        assertTrue(ChatStatus.inProgressStatus.contains(ChatStatus.VaDismissed))
        assertFalse(ChatStatus.inProgressStatus.contains(ChatStatus.Finished))
        assertFalse(ChatStatus.inProgressStatus.contains(ChatStatus.Canceled))
        assertFalse(ChatStatus.inProgressStatus.contains(ChatStatus.Failed))
    }

    @Test
    fun isInProgressTest() {
        assertTrue(ChatStatus.Queued.isInProgress())
        assertTrue(ChatStatus.Assigned.isInProgress())
        assertTrue(ChatStatus.VaAssigned.isInProgress())
        assertTrue(ChatStatus.Dismissed.isInProgress())
        assertTrue(ChatStatus.VaDismissed.isInProgress())
        assertFalse(ChatStatus.Finished.isInProgress())
        assertFalse(ChatStatus.Canceled.isInProgress())
        assertFalse(ChatStatus.Failed.isInProgress())
        assertFalse(ChatStatus.Unknown("unknown").isInProgress())
    }

    @Test
    fun isCompletedTest() {
        assertTrue(ChatStatus.Finished.isCompleted())
        assertTrue(ChatStatus.Canceled.isCompleted())
        assertTrue(ChatStatus.Failed.isCompleted())
        assertFalse(ChatStatus.Queued.isCompleted())
        assertFalse(ChatStatus.Assigned.isCompleted())
        assertFalse(ChatStatus.VaAssigned.isCompleted())
        assertFalse(ChatStatus.Dismissed.isCompleted())
        assertFalse(ChatStatus.VaDismissed.isCompleted())
        assertFalse(ChatStatus.Unknown("unknown").isCompleted())
    }

    @Test
    fun isDismissedTest() {
        assertTrue(ChatStatus.Dismissed.isDismissed())
        assertTrue(ChatStatus.VaDismissed.isDismissed())
        assertFalse(ChatStatus.Queued.isDismissed())
        assertFalse(ChatStatus.Assigned.isDismissed())
        assertFalse(ChatStatus.Finished.isDismissed())
        assertFalse(ChatStatus.Canceled.isDismissed())
        assertFalse(ChatStatus.Failed.isDismissed())
        assertFalse(ChatStatus.VaAssigned.isDismissed())
        assertFalse(ChatStatus.Unknown("unknown").isDismissed())
    }
}
