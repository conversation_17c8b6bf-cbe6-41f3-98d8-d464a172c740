package com.ccaiplatform.ccaichat.service

import android.net.Uri
import com.ccaiplatform.ccaichat.CCAIWebFormInterface
import com.ccaiplatform.ccaichat.interfaces.ChatNetworkError
import com.ccaiplatform.ccaichat.interfaces.ChatProviderInterface
import com.ccaiplatform.ccaichat.model.ChatEvent
import com.ccaiplatform.ccaichat.model.ChatHistoryResponse
import com.ccaiplatform.ccaichat.model.ChatInteraction
import com.ccaiplatform.ccaichat.model.ChatMessage
import com.ccaiplatform.ccaichat.model.ChatMessageBody
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.model.ChatTaskVaMessage
import com.ccaiplatform.ccaichat.model.CustomFormDetailsResponse
import com.ccaiplatform.ccaichat.model.Entries
import com.ccaiplatform.ccaichat.model.Form
import com.ccaiplatform.ccaichat.model.FormCompleteEventData
import com.ccaiplatform.ccaichat.model.MediaChatMessageRequest
import com.ccaiplatform.ccaichat.model.Pagination
import com.ccaiplatform.ccaichat.model.PlainTextChatMessageRequest
import com.ccaiplatform.ccaichat.model.SubmitCustomFormRequest
import com.ccaiplatform.ccaichat.model.TaskVaMessageRequest
import com.ccaiplatform.ccaichat.model.WebFormCompleteMessageRequest
import com.ccaiplatform.ccaichat.model.WebFormResponse
import com.ccaiplatform.ccaichat.model.enum.ChatMemberEvent
import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.ccaiplatform.ccaichat.model.enum.ChatMessageEvent
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichat.model.enum.ChatServiceError
import com.ccaiplatform.ccaichat.model.enum.ChatStatus
import com.ccaiplatform.ccaichat.model.enum.ChatTypingEvent
import com.ccaiplatform.ccaichat.model.enum.OutgoingMessageContent
import com.ccaiplatform.ccaikit.CCAI
import com.ccaiplatform.ccaikit.models.EndUser
import com.ccaiplatform.ccaikit.models.SmartAction
import com.ccaiplatform.ccaikit.models.SmartActionStatus
import com.ccaiplatform.ccaikit.models.SmartActionType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.Mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.whenever
import java.util.Date

class ChatServiceTest {

    @Mock
    private lateinit var networkService: ChatNetworkServiceInterface

    @Mock
    private lateinit var providerService: ChatProviderServiceInterface

    @Mock
    private lateinit var chatProvider: ChatProviderInterface

    @Mock
    private lateinit var listener: CCAIWebFormInterface

    private lateinit var chatService: ChatService

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
        chatService = ChatService(networkService, providerService, listener)
    }

    @Test
    fun startSuccess() {
        runBlocking {
            // Given
            val menuId = 123
            val chatResponse = ChatResponse(id = 456, providerType = "test")
            `when`(networkService.createChat(menuId)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            val eventDeferred = async {
                chatService.stateChangedSubject.first()
            }
            launch {
                chatService.start(menuId)
                verify(chatProvider).setListener(chatService)
                verify(chatProvider).join()
            }
            val receiveEvent = eventDeferred.await()
            assertEquals(ChatProviderState.Connecting, receiveEvent)
        }
    }

    @Test
    fun startFailureNoProvider() {
        assertThrows(ChatServiceError.FailedToGetProvider::class.java) {
            runBlocking {
                val menuId = 123
                val chatResponse = ChatResponse(id = 456)
                `when`(networkService.createChat(menuId)).thenReturn(Result.success(chatResponse))
                `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(null)
                chatService.start(menuId)
            }
        }
    }

    @Test
    fun startFailureCreateChatError() {
        assertThrows(Throwable::class.java) {
            runBlocking {
                val menuId = 123
                val chatResponse = ChatResponse(id = 456)
                `when`(networkService.createChat(menuId)).thenReturn(Result.failure(Throwable()))
                `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(null)
                chatService.start(menuId)
            }
        }
    }

    @Test
    fun sendTextMessage() {
        runBlocking {
            val message = OutgoingMessageContent.Text(content = "Hello")
            val messageRequest = PlainTextChatMessageRequest(type = ChatMessageBodyType.Text, content = "Hello")
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            chatService.sendMessage(message)
            verify(chatProvider).sendMessage(messageRequest)
        }
    }

    @Test
    fun sendPhotoMessage() {
        runBlocking {
            val photoBytes = listOf(byteArrayOf(1, 2, 3), byteArrayOf(4, 5, 6))
            val uris = listOf(Uri.parse("file://photo1"), Uri.parse("file://photo2"))
            val smartAction = SmartAction(1, SmartActionType.Photo, SmartActionStatus.Finished)
            val contentType = "image/jpeg"
            val photos = OutgoingMessageContent.Photos(
                photos = photoBytes,
                uris = uris,
                smartAction = smartAction,
                contentType = contentType
            )
            val mediaId = 666
            val messageRequest = MediaChatMessageRequest(type = ChatMessageBodyType.Photo, mediaId = mediaId)
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(networkService.uploadPhotos(456, photoBytes, "photo", smartAction)).thenReturn(Result.success(listOf(mediaId)))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            chatService.sendMessage(photos)
            verify(chatProvider).sendMessage(messageRequest)
        }
    }

    @Test
    fun sendFormMessage() {
        runBlocking {
            val data = FormCompleteEventData(status = "completed", timestamp = "2024-06-01T12:00:00Z", smartActionId = 123, details = null)
            val form = OutgoingMessageContent.FormComplete(
                type = null,
                signature = null,
                data = data
            )
            val messageRequest = WebFormCompleteMessageRequest(type = ChatMessageBodyType.FormComplete, data = data)
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            chatService.sendMessage(form)
            verify(chatProvider).sendMessage(messageRequest)
        }
    }

    @Test
    fun sendMessageWithTaskActive() {
        runBlocking {
            val message = OutgoingMessageContent.Text(content = "Hello")
            val expectedResponse = ChatTaskVaMessage(
                content = ChatMessageBody(content = "Test message"),
                id = 456,
                taskVaId = 789,
                participantId = 101
            )
            val messageRequest = TaskVaMessageRequest(type = ChatMessageBodyType.ServerMessage, messageId = expectedResponse.id, visibility = "task_virtual_agent")
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            whenever(networkService.sendTaskVaMessage(anyInt(), any())).thenReturn(Result.success(expectedResponse))
            chatService.start(456)
            chatService.javaClass.getDeclaredField("isTaskVaActive").apply {
                isAccessible = true
                set(chatService, true)
            }
            chatService.sendMessage(message)
            verify(chatProvider).sendMessage(messageRequest)
        }
    }

    @Test
    fun resume() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.resume(chatResponse)
            chatService.resume(chatResponse)
            verify(chatProvider, times(2)).join()
        }
    }

    @Test
    fun enqueueCurrentChat() {
        val chatResponse = ChatResponse(id = 456, status = ChatStatus.Finished)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(networkService.enqueue(456)).thenReturn(Result.success(chatResponse))
            `when`(networkService.getChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            chatService.enqueueCurrentChat()
            verify(networkService).getChat(456)
        }
    }

    @Test
    fun typing() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            chatService.typing(true)
            verify(chatProvider).typing(true)
        }
    }

    @Test
    fun sendMessagePreview() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            val preview = "Hello"
            chatService.sendMessagePreview(preview)
            verify(chatProvider).sendMessagePreview(preview)
        }
    }

    @Test
    fun sendMessagePreviewWithEmptyString() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            val preview = ""
            chatService.sendMessagePreview(preview)
            verify(chatProvider).sendMessagePreview(preview)
        }
    }

    @Test
    fun sendMessagePreviewWithSpecialCharacters() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            val preview = "Hello\nWorld\tTest\"Quote'"
            chatService.sendMessagePreview(preview)
            verify(chatProvider).sendMessagePreview(preview)
        }
    }

    @Test
    fun sendMessagePreviewWithUnicodeCharacters() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            val preview = "Hello 世界 🌍 Test"
            chatService.sendMessagePreview(preview)
            verify(chatProvider).sendMessagePreview(preview)
        }
    }

    @Test
    fun sendMessagePreviewWithVeryLongMessage() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            val preview = "A".repeat(1000)
            chatService.sendMessagePreview(preview)
            verify(chatProvider).sendMessagePreview(preview)
        }
    }

    @Test
    fun sendMessagePreviewMultipleTimes() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            
            // Send multiple message previews
            chatService.sendMessagePreview("First preview")
            chatService.sendMessagePreview("Second preview")
            chatService.sendMessagePreview("Third preview")
            
            // Verify each one is called
            verify(chatProvider).sendMessagePreview("First preview")
            verify(chatProvider).sendMessagePreview("Second preview")
            verify(chatProvider).sendMessagePreview("Third preview")
        }
    }

    @Test
    fun sendMessagePreviewWithWhitespaceOnly() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            val preview = "   \t\n  "
            chatService.sendMessagePreview(preview)
            verify(chatProvider).sendMessagePreview(preview)
        }
    }

    @Test
    fun sendMessagePreviewWithDifferentMessageTypes() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            
            // Test different types of message previews
            val messages = listOf(
                "Simple text message",
                "Message with numbers: 12345",
                "Message with symbols: @#$%^&*()",
                "Message with newlines:\nLine 1\nLine 2",
                "Message with tabs:\tTabbed\tcontent",
                "Message with quotes: \"quoted text\"",
                "Message with apostrophe: It's a test",
                "Message with backslash: \\escaped\\",
                "Message with unicode: Hello World 🌍",
                "Message with emoji: 😀🎉🚀"
            )
            
            messages.forEach { message ->
                chatService.sendMessagePreview(message)
                verify(chatProvider).sendMessagePreview(message)
            }
        }
    }

    @Test
    fun sendMessagePreviewWithProviderException() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            
            // Simulate provider throwing exception
            org.mockito.kotlin.doThrow(RuntimeException("Provider error"))
                .whenever(chatProvider).sendMessagePreview(any())
            
            val preview = "Hello"
            
            // Should throw exception
            assertThrows(RuntimeException::class.java) {
                chatService.sendMessagePreview(preview)
            }
        }
    }

    @Test
    fun sendMessagePreviewWithBoundaryValues() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            
            // Test boundary values
            val boundaryMessages = listOf(
                "", // Empty string
                " ", // Single space
                "\t", // Single tab
                "\n", // Single newline
                "a", // Single character
                "A".repeat(10000), // Very long string
                "\\", // Backslash
                "\"", // Double quote
                "'", // Single quote
                "`", // Backtick
                "0", // Numeric string
                "**********", // Pure numbers
                "!@#$%^&*()", // Special characters
                "Hello\nWorld", // Contains newline
                "Hello\tWorld", // Contains tab
                "Hello\r\nWorld", // Contains carriage return + newline
                "Hello\\World", // Contains backslash
                "Hello\"World", // Contains double quote
                "Hello'World", // Contains single quote
                "Hello`World" // Contains backtick
            )
            
            boundaryMessages.forEach { message ->
                chatService.sendMessagePreview(message)
                verify(chatProvider).sendMessagePreview(message)
            }
        }
    }

    @Test
    fun sendMessagePreviewWithUnicodeBoundaryCases() {
        val chatResponse = ChatResponse(id = 456)
        runBlocking {
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            
            // Test Unicode boundary cases
            val unicodeMessages = listOf(
                "\u0000", // Null character
                "\u0001", // Control character
                "\u007F", // Delete character
                "\u0080", // Extended ASCII
                "\u00FF", // Latin-1
                "\u0100", // Latin Extended
                "\uFFFF", // BMP end
                "\uD800", // High surrogate
                "\uDFFF", // Low surrogate
                "\uD800\uDFFF", // Valid surrogate pair
                "\uD800\uD800", // Invalid surrogate pair
                "\uDFFF\uDFFF", // Invalid surrogate pair
                "Hello\u0000World", // Contains null character
                "Hello\uD800World", // Contains high surrogate
                "Hello\uDFFFWorld", // Contains low surrogate
                "Hello\uD800\uDFFFWorld", // Contains valid surrogate pair
                "Hello\uD800\uD800World", // Contains invalid surrogate pair
                "Hello\uDFFF\uDFFFWorld" // Contains invalid surrogate pair
            )
            
            unicodeMessages.forEach { message ->
                chatService.sendMessagePreview(message)
                verify(chatProvider).sendMessagePreview(message)
            }
        }
    }

    @Test
    fun endChat() {
        val chatId = 456
        val chatResponse = ChatResponse(id = chatId)
        val menuId = 123
        runBlocking {
            `when`(networkService.createChat(menuId)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(123)
            chatService.endChat()
            verify(chatProvider).leave()
            verify(networkService).finishChat(chatId)
        }
    }

    @Test
    fun messagesReceived() {
        runBlocking(Dispatchers.IO) {
            val expectedMessage = ChatTaskVaMessage(
                content = ChatMessageBody(content = "Test message"),
                id = 111,
                taskVaId = 789,
                participantId = 101
            )

            fun createMessageList(type: ChatMessageBodyType, event: ChatMessageEvent, author: String) = ChatMessage(
                index = 1L,
                date = Date(),
                author = author,
                bodyRaw = ChatMessageBody(
                    type = type,
                    content = "Hello",
                    event = event
                ).toJSONString()
            )

            val messages = listOf(
                createMessageList(ChatMessageBodyType.ServerMessage, ChatMessageEvent.None, "end"),
                createMessageList(ChatMessageBodyType.Notification, ChatMessageEvent.TaskVaStarted, "end"),
                createMessageList(ChatMessageBodyType.Notification, ChatMessageEvent.TaskVaFinished, "end"),
                createMessageList(ChatMessageBodyType.Notification, ChatMessageEvent.TransferStarted, "end"),
                createMessageList(ChatMessageBodyType.Notification, ChatMessageEvent.TransferAccepted, "end"),
                createMessageList(ChatMessageBodyType.Notification, ChatMessageEvent.TransferFailed, "end"),
                createMessageList(ChatMessageBodyType.Text, ChatMessageEvent.None, "virtual_agent"),
                createMessageList(ChatMessageBodyType.Text, ChatMessageEvent.None, "virtual_agent"),
            )
            val chatId = 456
            val chatResponse = ChatResponse(id = chatId)
            `when`(networkService.createChat(123)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            whenever(networkService.getTaskVaMessage(anyInt(), any())).thenReturn(Result.success(expectedMessage))
            chatService.start(123)
            chatService.onMessagesReceived(messages)
        }
    }


    @Test
    fun typingEvent() {
        runBlocking {
            val typingEvent = ChatTypingEvent.Started("user1")
            val eventDeferred = async {
                chatService.typingEventSubject.first()
            }
            launch {
                chatService.onTypingEvent(typingEvent)
            }
            val receiveEvent = eventDeferred.await()
            assertEquals(receiveEvent, typingEvent)
        }
    }

    @Test
    fun memberEvent() {
        runBlocking {
            val memberEvent = ChatMemberEvent.Joined("")
            val eventDeferred = async {
                chatService.memberEventSubject.first()
            }
            launch {
                chatService.onMemberEvent(memberEvent)
            }
            val receiveEvent = eventDeferred.await()
            assertEquals(receiveEvent, memberEvent)
        }
    }

    @Test
    fun stateChanged() {
        runBlocking {
            val state = ChatProviderState.Connected
            val eventDeferred = async {
                chatService.stateChangedSubject.first()
            }
            launch {
                chatService.onStateChanged(state)
            }
            val receiveEvent = eventDeferred.await()
            assertEquals(receiveEvent, state)
        }
    }

    @Test
    fun escalate() {
        runBlocking {
            val escalationId = 123
            val deflectionChannel = "test_channel"
            val chatResponse = ChatResponse(id = 456)
            val idCaptor = argumentCaptor<Int>()
            val escalationIdCaptor = argumentCaptor<Int>()
            val channelCaptor = argumentCaptor<String>()

            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            chatService.escalate(escalationId, deflectionChannel)

            verify(networkService).escalateChatWithDeflection(idCaptor.capture(), escalationIdCaptor.capture(), channelCaptor.capture())

            assertEquals(escalationId, escalationIdCaptor.firstValue)
            assertEquals(deflectionChannel, channelCaptor.firstValue)
        }
    }

    @Test
    fun sendEvent() {
        runBlocking {
            val event = ChatEvent(
                name = "test_event",
                payload = mapOf("key" to "value")
            )
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            chatService.sendEvent(event)
            verify(networkService).postChatEvent(anyInt(), any())
        }
    }

    @Test
    fun getTaskVaMessageReturnsNull() {
        runBlocking {
            val messageId = 123
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            whenever(networkService.getTaskVaMessage(anyInt(), any())).thenReturn(Result.failure(Exception("No message found")))
            val result = chatService.getTaskVaMessage(messageId)
            assertEquals(null, result)
        }
    }

    @Test
    fun getTaskVaMessageReturnsMessage() {
        runBlocking {
            val messageId = 123
            val expectedMessage = ChatTaskVaMessage(
                content = ChatMessageBody(content = "Test message"),
                id = messageId,
                taskVaId = 789,
                participantId = 101
            )
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            whenever(networkService.getTaskVaMessage(anyInt(), any())).thenReturn(Result.success(expectedMessage))
            val result = chatService.getTaskVaMessage(messageId)
            assertEquals(expectedMessage, result)
        }
    }

    @Test
    fun sendTaskVaMessageReturnsNull() {
        runBlocking {
            val message = OutgoingMessageContent.Text(content = "Test message")
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            whenever(networkService.sendTaskVaMessage(anyInt(), any())).thenReturn(Result.failure(Exception("Failed to send message")))
            val result = chatService.sendTaskVaMessage(message)
            assertEquals(null, result)
        }
    }

    @Test
    fun sendTaskVaMessageReturnsMessage() {
        runBlocking {
            val message = OutgoingMessageContent.Text(content = "Test message")
            val expectedResponse = ChatTaskVaMessage(
                content = ChatMessageBody(content = "Test message"),
                id = 456,
                taskVaId = 789,
                participantId = 101
            )
            val chatResponse = ChatResponse(id = 456)
            `when`(networkService.createChat(456)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(456)
            whenever(networkService.sendTaskVaMessage(anyInt(), any())).thenReturn(Result.success(expectedResponse))
            val result = chatService.sendTaskVaMessage(message)
            assertEquals(expectedResponse, result)
        }
    }

    @Test
    fun getCustomFormDetails() {
        runBlocking {
            val formId = 123
            val expectedResponse = CustomFormDetailsResponse(
                title = "title",
                header = "",
                footer = "footer"
            )
            `when`(chatService.getCustomFormDetails(formId)).thenReturn(expectedResponse)
            val result = chatService.getCustomFormDetails(formId)
            assertEquals(expectedResponse, result)
        }
    }

    @Test
    fun submitCustomFormReturnsUnit() {
        runBlocking {
            val request = SubmitCustomFormRequest(
                smartActionId = 123,
                formResponse = listOf(
                    SubmitCustomFormRequest.Answer(questionId = 1, value = "Answer 1"),
                    SubmitCustomFormRequest.Answer(questionId = 2, value = "Answer 2")
                )
            )
            `when`(chatService.submitCustomForm(request)).thenReturn(true)
            val result = chatService.submitCustomForm(request)
            assertEquals(true, result)
        }
    }

    @Test
    fun getLastChatInProgressTest() {
        runBlocking {
            val chatInProgress = ChatResponse(id = 123)
            val chatsInProgress = listOf(chatInProgress)
            `when`(networkService.getChatsFilterInProgress()).thenReturn(Result.success(chatsInProgress))

            val result = chatService.getLastChatInProgress()

            assertEquals(chatInProgress, result)
        }
    }

    @Test
    fun checkStatusTest() {
        runBlocking {
            val chatId = 456
            val chatResponse = ChatResponse(id = chatId)

            // Setup initial chat
            `when`(networkService.createChat(123)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(123)

            // Mock getChat for checkStatus
            `when`(networkService.getChat(chatId)).thenReturn(Result.success(chatResponse))

            chatService.checkStatus()

            verify(networkService).getChat(chatId)
        }
    }

    @Test
    fun getPreviousChatsTest() {
        runBlocking {
            val page = 1
            val perPage = 10
            val endUser = EndUser(
                id = 123,
                name = "Test User",
                email = "<EMAIL>"
            )
            val pagination = Pagination(nextPage = 3, perPage = 20)
            val interaction1 = ChatInteraction(
                commType = "live_chat",
                commId = 456,
                transcriptVersion = "v1.0",
                assignedAt = "2023-01-01T00:00:00Z",
                timezone = "UTC",
                entries = listOf(
                    Entries(
                        body = ChatMessageBody(messageId = 333, type = ChatMessageBodyType.ServerMessage)
                    )
                )
            )
            val interaction2 = ChatInteraction(
                commType = "live_chat2",
                commId = 456,
                transcriptVersion = "v1.0",
                assignedAt = "2023-01-01T00:00:00Z",
                timezone = "UTC",
                entries = listOf(
                    Entries(
                        body = ChatMessageBody(
                            type = ChatMessageBodyType.FormComplete,
                            content = "Hello",
                            data = FormCompleteEventData(status = "success"),
                            form = Form.WebForm()
                        )
                    )
                )
            )
            val historyResponse = ChatHistoryResponse(
                chats = listOf(interaction1, interaction2),
                pagination = pagination
            )
            val taskMessageList = listOf(ChatTaskVaMessage(content = ChatMessageBody(), id = 333, taskVaId = 333, participantId = 333))
            `when`(networkService.getHistoryMessages(page, perPage)).thenReturn(Result.success(historyResponse))
            `when`(networkService.getTaskVaMessages(111)).thenReturn(Result.success(taskMessageList))
            chatService.javaClass.getDeclaredField("currentChatId").apply {
                isAccessible = true
                set(chatService, 111)
            }
            val result1 = chatService.getPreviousChats(page, perPage)
            assertEquals(null, result1)
            CCAI.setEndUser(endUser)
            val result2 = chatService.getPreviousChats(page, perPage)
            CCAI.setEndUser(null)
            assertEquals(3, result2?.nextPage)
        }
    }

    @Test
    fun getFormUrl() {
        runBlocking {
            `when`(listener.handleWebFormRequest(any())).thenReturn(WebFormResponse())
            chatService.getFormUrl("1", 1)
        }
    }

    @Test
    fun getFormUrlWithError() {
        assertThrows(ChatNetworkError.InvalidWebFormInterface::class.java) {
            runBlocking {
                chatService.javaClass.getDeclaredField("webFormInterface").apply {
                    isAccessible = true
                    set(chatService, null)
                }
                chatService.getFormUrl("1", 1)
            }
        }
    }

    @Test
    fun verifyFormData() {
        runBlocking {
            `when`(networkService.verifyFormData(any())).thenReturn(Result.success(Unit))
            val result = chatService.verifyFormData(WebFormResponse())
            assertEquals(result, true)
        }
    }

    @Test
    fun escalateToHumanAgent() {
        runBlocking {
            val chatId = 456
            val chatResponse = ChatResponse(id = chatId)
            `when`(networkService.escalateToHumanAgent(any())).thenReturn(Unit)
            `when`(networkService.createChat(123)).thenReturn(Result.success(chatResponse))
            `when`(networkService.getChat(chatId)).thenReturn(Result.success(chatResponse))
            `when`(providerService.providerForResponse(chatResponse, networkService)).thenReturn(chatProvider)
            chatService.start(123)
            chatService.escalateToHumanAgent()
            verify(networkService).getChat(chatId)
        }
    }
}
