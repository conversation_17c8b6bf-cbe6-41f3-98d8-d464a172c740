package com.ccaiplatform.ccaichat.model

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ChatEventTest {

    @Test
    fun shouldCreateChatEvent() {
        val event = ChatEvent(name = "test_event", payload = mapOf("key" to "value"))
        assertEquals("test_event", event.name)
        assertEquals(mapOf("key" to "value"), event.payload)
    }

    @Test
    fun shouldCreateChatEventWithEmptyPayload() {
        val event = ChatEvent(name = "test_event", payload = emptyMap())
        assertEquals("test_event", event.name)
        assertEquals(emptyMap<String, String>(), event.payload)
    }
}
