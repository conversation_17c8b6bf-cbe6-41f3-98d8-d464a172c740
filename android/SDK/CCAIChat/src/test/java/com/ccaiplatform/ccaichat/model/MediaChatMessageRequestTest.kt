package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class MediaChatMessageRequestTest {

    @Test
    fun constructorWithValidParameters() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 123
        )
        
        assertEquals(ChatMessageBodyType.Photo, request.type)
        assertEquals(123, request.mediaId)
    }

    @Test
    fun constructorWithVideoType() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Video,
            mediaId = 456
        )
        
        assertEquals(ChatMessageBodyType.Video, request.type)
        assertEquals(456, request.mediaId)
    }

    @Test
    fun constructorWithImageType() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Image,
            mediaId = 789
        )
        
        assertEquals(ChatMessageBodyType.Image, request.type)
        assertEquals(789, request.mediaId)
    }

    @Test
    fun constructorWithZeroMediaId() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 0
        )
        
        assertEquals(ChatMessageBodyType.Photo, request.type)
        assertEquals(0, request.mediaId)
    }

    @Test
    fun constructorWithNegativeMediaId() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = -1
        )
        
        assertEquals(ChatMessageBodyType.Photo, request.type)
        assertEquals(-1, request.mediaId)
    }

    @Test
    fun constructorWithLargeMediaId() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Video,
            mediaId = Int.MAX_VALUE
        )
        
        assertEquals(ChatMessageBodyType.Video, request.type)
        assertEquals(Int.MAX_VALUE, request.mediaId)
    }

    @Test
    fun implementsChatMessageRequestInterface() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 123
        )
        
        assertTrue(request is ChatMessageRequest)
        assertEquals(ChatMessageBodyType.Photo, request.type)
    }

    @Test
    fun toJSONStringReturnsValidJson() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 123
        )
        
        val json = request.toJSONString()
        assertNotNull(json)
        assertTrue(json!!.contains("\"type\""))
        assertTrue(json.contains("\"media_id\""))
        assertTrue(json.contains("123"))
    }

    @Test
    fun toJSONStringWithVideoType() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Video,
            mediaId = 456
        )
        
        val json = request.toJSONString()
        assertNotNull(json)
        assertTrue(json!!.contains("\"media_id\":456"))
    }

    @Test
    fun dataClassEquality() {
        val request1 = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 123
        )
        
        val request2 = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 123
        )
        
        assertEquals(request1, request2)
        assertEquals(request1.hashCode(), request2.hashCode())
    }

    @Test
    fun dataClassInequality() {
        val request1 = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 123
        )
        
        val request2 = MediaChatMessageRequest(
            type = ChatMessageBodyType.Video,
            mediaId = 123
        )
        
        assertTrue(request1 != request2)
    }

    @Test
    fun dataClassCopy() {
        val original = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 123
        )
        
        val copied = original.copy(mediaId = 456)
        
        assertEquals(ChatMessageBodyType.Photo, copied.type)
        assertEquals(456, copied.mediaId)
        assertEquals(original.type, copied.type)
    }

    @Test
    fun dataClassToString() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Photo,
            mediaId = 123
        )
        
        val toString = request.toString()
        assertNotNull(toString)
        assertTrue(toString.contains("MediaChatMessageRequest"))
        assertTrue(toString.contains("123"))
    }

    @Test
    fun dataClassDestructuring() {
        val request = MediaChatMessageRequest(
            type = ChatMessageBodyType.Video,
            mediaId = 789
        )
        
        val (type, mediaId) = request
        assertEquals(ChatMessageBodyType.Video, type)
        assertEquals(789, mediaId)
    }

    @Test
    fun withDifferentMessageBodyTypes() {
        val types = listOf(
            ChatMessageBodyType.Photo,
            ChatMessageBodyType.Video,
            ChatMessageBodyType.Image,
            ChatMessageBodyType.Document
        )
        
        types.forEach { bodyType ->
            val request = MediaChatMessageRequest(
                type = bodyType,
                mediaId = 100
            )
            
            assertEquals(bodyType, request.type)
            assertEquals(100, request.mediaId)
        }
    }
}
