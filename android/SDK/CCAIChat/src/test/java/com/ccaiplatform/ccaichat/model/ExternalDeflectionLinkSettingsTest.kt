package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaikit.models.response.queuemenu.channel.ExternalDeflectionLink
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class ExternalDeflectionLinkSettingsTest {

    private val gson: Gson = GsonBuilder().create()

    @Test
    fun constructionAndEquality() {
        val link = ExternalDeflectionLink("http://example.com", links = null)
        val settings1 = ExternalDeflectionLinkSettings(
            enabled = true,
            deflectionLinkRendered = listOf(link)
        )
        val settings2 = ExternalDeflectionLinkSettings(
            enabled = true,
            deflectionLinkRendered = listOf(link)
        )
        assertEquals(settings1, settings2)
        assertTrue(settings1.enabled == true)
        assertEquals(1, settings1.deflectionLinkRendered?.size)
        assertEquals(link, settings1.deflectionLinkRendered?.get(0))
    }

    @Test
    fun gsonSerializationDeserialization() {
        val link = ExternalDeflectionLink("http://example.com", links = null)
        val settings = ExternalDeflectionLinkSettings(
            enabled = false,
            deflectionLinkRendered = listOf(link)
        )
        val json = gson.toJson(settings)
        val fromJson = gson.fromJson(json, ExternalDeflectionLinkSettings::class.java)
        assertEquals(settings, fromJson)
    }

    @Test
    fun nullFields() {
        val settings = ExternalDeflectionLinkSettings()
        assertNull(settings.enabled)
        assertNull(settings.deflectionLinkRendered)
    }
}
