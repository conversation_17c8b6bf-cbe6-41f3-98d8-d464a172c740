package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaikit.util.GsonProvider
import com.google.gson.JsonSyntaxException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNotSame
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class WebFormMessageTest {

    @Test
    fun defaultConstructor() {
        val webFormMessage = WebFormMessage()
        
        assertNull(webFormMessage.externalFormId)
        assertEquals(0, webFormMessage.smartActionId)
        assertNull(webFormMessage.title)
        assertNull(webFormMessage.name)
    }

    @Test
    fun constructorWithParameters() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        assertEquals("ext_form_123", webFormMessage.externalFormId)
        assertEquals(456, webFormMessage.smartActionId)
        assertEquals("Test Form", webFormMessage.title)
        assertEquals("test_form", webFormMessage.name)
    }

    @Test
    fun nullValues() {
        val webFormMessage = WebFormMessage(
            externalFormId = null,
            smartActionId = null,
            title = null,
            name = null
        )
        
        assertNull(webFormMessage.externalFormId)
        assertNull(webFormMessage.smartActionId)
        assertNull(webFormMessage.title)
        assertNull(webFormMessage.name)
    }

    @Test
    fun emptyStrings() {
        val webFormMessage = WebFormMessage(
            externalFormId = "",
            smartActionId = 0,
            title = "",
            name = ""
        )
        
        assertEquals("", webFormMessage.externalFormId)
        assertEquals(0, webFormMessage.smartActionId)
        assertEquals("", webFormMessage.title)
        assertEquals("", webFormMessage.name)
    }

    @Test
    fun negativeSmartActionId() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = -1,
            title = "Test Form",
            name = "test_form"
        )
        
        assertEquals("ext_form_123", webFormMessage.externalFormId)
        assertEquals(-1, webFormMessage.smartActionId)
        assertEquals("Test Form", webFormMessage.title)
        assertEquals("test_form", webFormMessage.name)
    }

    @Test
    fun largeValues() {
        val webFormMessage = WebFormMessage(
            externalFormId = "a".repeat(1000),
            smartActionId = Int.MAX_VALUE,
            title = "b".repeat(500),
            name = "c".repeat(300)
        )
        
        assertEquals("a".repeat(1000), webFormMessage.externalFormId)
        assertEquals(Int.MAX_VALUE, webFormMessage.smartActionId)
        assertEquals("b".repeat(500), webFormMessage.title)
        assertEquals("c".repeat(300), webFormMessage.name)
    }

    @Test
    fun jsonSerialization() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val json = GsonProvider.gson.toJson(webFormMessage)
        
        assertNotNull(json)
        assertTrue(json.contains("\"external_form_id\":\"ext_form_123\""))
        assertTrue(json.contains("\"smart_action_id\":456"))
        assertTrue(json.contains("\"title\":\"Test Form\""))
        assertTrue(json.contains("\"name\":\"test_form\""))
    }

    @Test
    fun jsonSerializationWithNulls() {
        val webFormMessage = WebFormMessage(
            externalFormId = null,
            smartActionId = null,
            title = null,
            name = null
        )
        
        val json = GsonProvider.gson.toJson(webFormMessage)
        
        assertNotNull(json)
        assertTrue(json.contains("{}") || json.contains("null"))
    }

    @Test
    fun jsonDeserialization() {
        val json = """
            {
                "external_form_id": "ext_form_123",
                "smart_action_id": 456,
                "title": "Test Form",
                "name": "test_form"
            }
        """.trimIndent()
        
        val webFormMessage = GsonProvider.gson.fromJson(json, WebFormMessage::class.java)
        
        assertNotNull(webFormMessage)
        assertEquals("ext_form_123", webFormMessage.externalFormId)
        assertEquals(456, webFormMessage.smartActionId)
        assertEquals("Test Form", webFormMessage.title)
        assertEquals("test_form", webFormMessage.name)
    }

    @Test
    fun jsonDeserializationWithMissingFields() {
        val json = """
            {
                "external_form_id": "ext_form_123"
            }
        """.trimIndent()
        
        val webFormMessage = GsonProvider.gson.fromJson(json, WebFormMessage::class.java)
        
        assertNotNull(webFormMessage)
        assertEquals("ext_form_123", webFormMessage.externalFormId)
        assertEquals(0, webFormMessage.smartActionId)
        assertNull(webFormMessage.title)
        assertNull(webFormMessage.name)
    }

    @Test
    fun jsonDeserializationWithNulls() {
        val json = """
            {
                "external_form_id": null,
                "smart_action_id": null,
                "title": null,
                "name": null
            }
        """.trimIndent()
        
        val webFormMessage = GsonProvider.gson.fromJson(json, WebFormMessage::class.java)
        
        assertNotNull(webFormMessage)
        assertNull(webFormMessage.externalFormId)
        assertNull(webFormMessage.smartActionId)
        assertNull(webFormMessage.title)
        assertNull(webFormMessage.name)
    }

    @Test
    fun jsonRoundTrip() {
        val originalWebFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val json = GsonProvider.gson.toJson(originalWebFormMessage)
        val deserializedWebFormMessage = GsonProvider.gson.fromJson(json, WebFormMessage::class.java)
        
        assertEquals(originalWebFormMessage, deserializedWebFormMessage)
    }

    @Test
    fun jsonDeserializationWithInvalidJson() {
        val invalidJson = "{ invalid json }"
        assertThrows(JsonSyntaxException::class.java) {
            GsonProvider.gson.fromJson(invalidJson, WebFormMessage::class.java)
        }
    }

    @Test
    fun dataClassEquals() {
        val webFormMessage1 = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val webFormMessage2 = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val webFormMessage3 = WebFormMessage(
            externalFormId = "ext_form_456",
            smartActionId = 789,
            title = "Different Form",
            name = "different_form"
        )
        
        assertEquals(webFormMessage1, webFormMessage2)
        assertNotEquals(webFormMessage1, webFormMessage3)
        assertNotEquals(webFormMessage1, null)
        assertNotEquals(webFormMessage1, "not a WebFormMessage")
    }

    @Test
    fun dataClassHashCode() {
        val webFormMessage1 = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val webFormMessage2 = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        assertEquals(webFormMessage1.hashCode(), webFormMessage2.hashCode())
    }

    @Test
    fun dataClassToString() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val toStringResult = webFormMessage.toString()
        
        assertNotNull(toStringResult)
        assertTrue(toStringResult.contains("WebFormMessage"))
        assertTrue(toStringResult.contains("ext_form_123"))
        assertTrue(toStringResult.contains("456"))
        assertTrue(toStringResult.contains("Test Form"))
        assertTrue(toStringResult.contains("test_form"))
    }

    @Test
    fun dataClassCopy() {
        val originalWebFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val copiedWebFormMessage = originalWebFormMessage.copy()
        
        assertEquals(originalWebFormMessage, copiedWebFormMessage)
        assertNotSame(originalWebFormMessage, copiedWebFormMessage)
    }

    @Test
    fun dataClassCopyWithChanges() {
        val originalWebFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val modifiedWebFormMessage = originalWebFormMessage.copy(
            externalFormId = "ext_form_modified",
            smartActionId = 789
        )
        
        assertEquals("ext_form_modified", modifiedWebFormMessage.externalFormId)
        assertEquals(789, modifiedWebFormMessage.smartActionId)
        assertEquals("Test Form", modifiedWebFormMessage.title)
        assertEquals("test_form", modifiedWebFormMessage.name)
    }

    @Test
    fun formMessageInterface() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        assertTrue(webFormMessage is FormMessage)
        assertEquals(456, webFormMessage.smartActionId)
        assertEquals("Test Form", webFormMessage.title)
        assertEquals("test_form", webFormMessage.name)
    }

    @Test
    fun formMessageInterfaceWithNulls() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = null,
            title = null,
            name = null
        )
        
        assertTrue(webFormMessage is FormMessage)
        assertNull(webFormMessage.smartActionId)
        assertNull(webFormMessage.title)
        assertNull(webFormMessage.name)
    }

    @Test
    fun specialCharacters() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123!@#$%^&*()",
            smartActionId = 456,
            title = "Test Form with special characters and symbols 123!@#$%^&*()",
            name = "test_form_with_unicode_chars_123!@#$%^&*()"
        )
        
        assertEquals("ext_form_123!@#$%^&*()", webFormMessage.externalFormId)
        assertEquals(456, webFormMessage.smartActionId)
        assertEquals("Test Form with special characters and symbols 123!@#$%^&*()", webFormMessage.title)
        assertEquals("test_form_with_unicode_chars_123!@#$%^&*()", webFormMessage.name)
    }

    @Test
    fun jsonSerializationWithSpecialCharacters() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123!@#$%^&*()",
            smartActionId = 456,
            title = "Test Form with special characters",
            name = "test_form_with_unicode_chars"
        )
        
        val json = GsonProvider.gson.toJson(webFormMessage)
        val deserializedWebFormMessage = GsonProvider.gson.fromJson(json, WebFormMessage::class.java)
        
        assertEquals(webFormMessage, deserializedWebFormMessage)
    }

    @Test
    fun dataClassComponentFunctions() {
        val webFormMessage = WebFormMessage(
            externalFormId = "ext_form_123",
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val (externalFormId, smartActionId, title, name) = webFormMessage
        
        assertEquals("ext_form_123", externalFormId)
        assertEquals(456, smartActionId)
        assertEquals("Test Form", title)
        assertEquals("test_form", name)
    }
} 
