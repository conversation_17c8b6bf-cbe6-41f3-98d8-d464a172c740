package com.ccaiplatform.ccaichat.service

import android.content.Context
import com.ccaiplatform.ccaichat.interfaces.ChatProviderInterface
import com.ccaiplatform.ccaichat.interfaces.ChatProviderModuleInterface
import com.ccaiplatform.ccaichat.model.ChatOptions
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaikit.ModuleLocator
import com.ccaiplatform.ccaikit.interfaces.ProviderModuleInterface
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations

class ChatProviderServiceTest {

    @Mock
    private lateinit var context: Context

    @Mock
    private lateinit var networkService: ChatNetworkServiceInterface

    private lateinit var chatProviderService: ChatProviderService

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
        chatProviderService = ChatProviderService(context)
    }

    @Test
    fun providerForResponseWithNullProviderType() {
        val response = ChatResponse(id = 1, providerType = null)
        val result = chatProviderService.providerForResponse(response, networkService)
        assertNull(result)
    }

    @Test
    fun providerForResponseWithInvalidProvider() {
        val providerName = "invalid_provider"
        val response = ChatResponse(id = 1, providerType = providerName)
        `when`(ModuleLocator.moduleByProviderName(providerName)).thenReturn(null)
        val result = chatProviderService.providerForResponse(response, networkService)
        assertNull(result)
    }

    @Test
    fun providerForResponseWithNonChatProviderModule() {
        val providerName = "test_provider_1"
        val response = ChatResponse(id = 1, providerType = providerName)
        val mockModule = mock(ProviderModuleInterface::class.java)
        `when`(mockModule.providerName).thenReturn("test_provider_1")
        ModuleLocator.register(mockModule)
        val result = chatProviderService.providerForResponse(response, networkService)
        assertNull(result)
    }

    @Test
    fun providerForResponseWithOptions() {
        val providerName = "test_provider_2"
        val response = ChatResponse(id = 1, providerType = providerName)
        val options = ChatOptions()
        val mockModule = mock(ChatProviderModuleInterface::class.java)
        val mockProvider = mock(ChatProviderInterface::class.java)
        ModuleLocator.register(mockModule)
        `when`(mockModule.providerName).thenReturn("test_provider_2")
        `when`(mockModule.chatProvider(context, response, options, networkService)).thenReturn(mockProvider)
        val serviceWithOptions = ChatProviderService(context, options)
        val result = serviceWithOptions.providerForResponse(response, networkService)
        assertEquals(result,mockProvider)
    }
}
