package com.ccaiplatform.ccaichat.interfaces

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ChatNetworkErrorTest {

    @Test
    fun invalidChat() {
        val error = ChatNetworkError.InvalidChat
        assertTrue(error is ChatNetworkError)
        assertTrue(error is Throwable)
        assertEquals("InvalidChat", error::class.simpleName)
    }

    @Test
    fun invalidCurrentChatId() {
        val error = ChatNetworkError.InvalidCurrentChatId
        assertTrue(error is ChatNetworkError)
        assertTrue(error is Throwable)
        assertEquals("InvalidCurrentChatId", error::class.simpleName)
    }

    @Test
    fun emptyPhotos() {
        val error = ChatNetworkError.EmptyPhotos
        assertTrue(error is ChatNetworkError)
        assertTrue(error is Throwable)
        assertEquals("EmptyPhotos", error::class.simpleName)
    }

    @Test
    fun emptyVideos() {
        val error = ChatNetworkError.EmptyVideos
        assertTrue(error is ChatNetworkError)
        assertTrue(error is Throwable)
        assertEquals("EmptyVideos", error::class.simpleName)
    }

    @Test
    fun invalidTaskVaMessage() {
        val error = ChatNetworkError.InvalidTaskVaMessage
        assertTrue(error is ChatNetworkError)
        assertTrue(error is Throwable)
        assertEquals("InvalidTaskVaMessage", error::class.simpleName)
    }

    @Test
    fun invalidCustomFormAnswer() {
        val error = ChatNetworkError.InvalidCustomFormAnswer
        assertTrue(error is ChatNetworkError)
        assertTrue(error is Throwable)
        assertEquals("InvalidCustomFormAnswer", error::class.simpleName)
    }
}
