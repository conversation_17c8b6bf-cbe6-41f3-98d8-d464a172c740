package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaikit.models.enums.DeflectionType
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class DeflectionTest {

    private val gson: Gson = GsonBuilder().create()

    @Test
    fun deflectionConstruction() {
        val options = DeflectionOptions() // Replace with actual constructor if needed
        val deflection = Deflection(
            type = DeflectionType.AfterHours,
            waitEstimation = 10,
            options = options
        )
        assertEquals(DeflectionType.AfterHours, deflection.type)
        assertEquals(10, deflection.waitEstimation)
        assertEquals(options, deflection.options)
    }

    @Test
    fun deflectionEquality() {
        val d1 = Deflection(DeflectionType.OverCapacity, 5, null)
        val d2 = Deflection(DeflectionType.OverCapacity, 5, null)
        assertEquals(d1, d2)
    }

    @Test
    fun gsonSerializationDeserialization() {
        val deflection = Deflection(DeflectionType.ManualRedirection, 15, null)
        val json = gson.toJson(deflection)
        val fromJson = gson.fromJson(json, Deflection::class.java)
        assertEquals(deflection, fromJson)
    }
}
