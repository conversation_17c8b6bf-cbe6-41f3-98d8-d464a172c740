package com.ccaiplatform.ccaichat.service

import com.ccaiplatform.ccaichat.model.ChatEvent
import com.ccaiplatform.ccaichat.model.ChatMessage
import com.ccaiplatform.ccaichat.model.ChatMessageBody
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.model.ChatTaskVaMessage
import com.ccaiplatform.ccaichat.model.CustomFormDetailsResponse
import com.ccaiplatform.ccaichat.model.SubmitCustomFormRequest
import com.ccaiplatform.ccaichat.model.enum.ChatMemberEvent
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichat.model.enum.ChatTypingEvent
import com.ccaiplatform.ccaichat.model.enum.OutgoingMessageContent
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations

class ChatServiceInterfaceTest {

    @Mock
    private lateinit var chatService: ChatServiceInterface

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun start() {
        val menuId = 123
        runBlocking {
            chatService.start(menuId)
        }
        runBlocking {
            verify(chatService).start(menuId)
        }
    }

    @Test
    fun sendMessage() {
        val message = OutgoingMessageContent.Text(content = "Test message")
        runBlocking {
            chatService.sendMessage(message)
        }
        runBlocking {
            verify(chatService).sendMessage(message)
        }
    }

    @Test
    fun typing() {
        val isTyping = true
        chatService.typing(isTyping)
        verify(chatService).typing(isTyping)
    }

    @Test
    fun sendMessagePreview() {
        val messagePreview = "Hello"
        chatService.sendMessagePreview(messagePreview)
        verify(chatService).sendMessagePreview(messagePreview)
    }

    @Test
    fun endChat() {
        runBlocking {
            chatService.endChat()
        }
        runBlocking {
            verify(chatService).endChat()
        }
    }

    @Test
    fun messagesReceivedFlow() = runBlocking {
        val messagesFlow = MutableSharedFlow<List<ChatMessage>>()
        `when`(chatService.messagesReceivedSubject).thenReturn(messagesFlow)
        
        val mockMessage = mock(ChatMessage::class.java)
        val messages = listOf(mockMessage)
        
        val eventDeferred = async {
            chatService.messagesReceivedSubject.first()
        }
        launch {
            messagesFlow.emit(messages)
        }
        val receiveEvent = eventDeferred.await()
        assertEquals(messages, receiveEvent)
    }

    @Test
    fun typingEventFlow() = runBlocking {
        val typingEventFlow = MutableSharedFlow<ChatTypingEvent>()
        `when`(chatService.typingEventSubject).thenReturn(typingEventFlow)
        
        val typingEvent = ChatTypingEvent.Started("user1")
        val eventDeferred = async {
            chatService.typingEventSubject.first()
        }
        launch {
            typingEventFlow.emit(typingEvent)
        }
        val receiveEvent = eventDeferred.await()
        assertEquals(typingEvent, receiveEvent)
    }

    @Test
    fun memberEventFlow() = runBlocking {
        val memberEventFlow = MutableSharedFlow<ChatMemberEvent>()
        `when`(chatService.memberEventSubject).thenReturn(memberEventFlow)
        
        val memberEvent = ChatMemberEvent.Joined("")
        val eventDeferred = async {
            chatService.memberEventSubject.first()
        }
        launch {
            memberEventFlow.emit(memberEvent)
        }
        val receiveEvent = eventDeferred.await()
        assertEquals(memberEvent, receiveEvent)
    }

    @Test
    fun stateChangedFlow() = runBlocking {
        val stateChangedFlow = MutableSharedFlow<ChatProviderState>()
        `when`(chatService.stateChangedSubject).thenReturn(stateChangedFlow)
        
        val state = ChatProviderState.Connected
        val eventDeferred = async {
            chatService.stateChangedSubject.first()
        }
        launch {
            stateChangedFlow.emit(state)
        }
        val receiveEvent = eventDeferred.await()
        assertEquals(state, receiveEvent)
    }

    @Test
    fun resume() = runBlocking {
        val chat = ChatResponse(id = 1)
        chatService.resume(chat)
        verify(chatService).resume(chat)
    }

    @Test
    fun enqueueCurrentChat() = runBlocking {
        chatService.enqueueCurrentChat()
        verify(chatService).enqueueCurrentChat()
    }

    @Test
    fun getLastChatInProgressReturnsNull() = runBlocking {
        `when`(chatService.getLastChatInProgress()).thenReturn(null)
        val result = chatService.getLastChatInProgress()
        assertEquals(null, result)
    }

    @Test
    fun getLastChatInProgressReturnsChat() = runBlocking {
        val chat = ChatResponse(id = 2)
        `when`(chatService.getLastChatInProgress()).thenReturn(chat)
        val result = chatService.getLastChatInProgress()
        assertEquals(chat, result)
    }

    @Test
    fun chatReceivedSubjectFlow() = runBlocking {
        val flow = MutableSharedFlow<ChatResponse>()
        val chat = ChatResponse(id = 3)
        `when`(chatService.chatReceivedSubject).thenReturn(flow)
        val eventDeferred = async { chatService.chatReceivedSubject.first() }
        launch { flow.emit(chat) }
        val receiveEvent = eventDeferred.await()
        assertEquals(chat, receiveEvent)
    }

    @Test
    fun checkStatusSubjectFlow() = runBlocking {
        val flow = MutableSharedFlow<Map<String, Any>>()
        val map = mapOf("check" to true)
        `when`(chatService.checkStatusSubject).thenReturn(flow)
        val eventDeferred = async { chatService.checkStatusSubject.first() }
        launch { flow.emit(map) }
        val receiveEvent = eventDeferred.await()
        assertEquals(map, receiveEvent)
    }

    @Test
    fun messageSendResultSubjectFlow() = runBlocking {
        val flow = MutableSharedFlow<Boolean>()
        `when`(chatService.messageSendResultSubject).thenReturn(flow)
        val eventDeferred = async { chatService.messageSendResultSubject.first() }
        launch { flow.emit(true) }
        val receiveEvent = eventDeferred.await()
        assertEquals(true, receiveEvent)
    }

    @Test
    fun escalateChat() = runBlocking {
        chatService.escalateToHumanAgent()
        verify(chatService).escalateToHumanAgent()
    }

    @Test
    fun checkStatus() = runBlocking {
        chatService.checkStatus()
        verify(chatService).checkStatus()
    }

    @Test
    fun getTaskVaMessageReturnsNull() = runBlocking {
        val messageId = 123
        `when`(chatService.getTaskVaMessage(messageId)).thenReturn(null)
        val result = chatService.getTaskVaMessage(messageId)
        assertEquals(null, result)
    }

    @Test
    fun getTaskVaMessageReturnsMessage() = runBlocking {
        val messageId = 123
        val expectedMessage = ChatTaskVaMessage(
            content = ChatMessageBody(content = "Test message"),
            id = messageId,
            taskVaId = 789,
            participantId = 101
        )
        `when`(chatService.getTaskVaMessage(messageId)).thenReturn(expectedMessage)
        val result = chatService.getTaskVaMessage(messageId)
        assertEquals(expectedMessage, result)
    }

    @Test
    fun sendTaskVaMessageReturnsNull() = runBlocking {
        val message = OutgoingMessageContent.Text(content = "Test message")
        `when`(chatService.sendTaskVaMessage(message)).thenReturn(null)
        val result = chatService.sendTaskVaMessage(message)
        assertEquals(null, result)
    }

    @Test
    fun sendTaskVaMessageReturnsMessage() = runBlocking {
        val message = OutgoingMessageContent.Text("Test message")
        val expectedResponse = ChatTaskVaMessage(
            content = ChatMessageBody(content = "Test message"),
            id = 456,
            taskVaId = 789,
            participantId = 101
        )
        `when`(chatService.sendTaskVaMessage(message)).thenReturn(expectedResponse)
        val result = chatService.sendTaskVaMessage(message)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun escalate() = runBlocking {
        val escalationId = 123
        val deflectionChannel = "test_channel"
        chatService.escalate(escalationId, deflectionChannel)
        verify(chatService).escalate(escalationId, deflectionChannel)
    }

    @Test
    fun sendEvent() = runBlocking {
        val event = ChatEvent(
            name = "test_event",
            payload = mapOf("key" to "value")
        )
        chatService.sendEvent(event)
        verify(chatService).sendEvent(event)
    }

    @Test
    fun getCustomFormDetails() = runBlocking {
        val formId = 123
        val expectedResponse = CustomFormDetailsResponse(
            title = "title",
            header = "",
            footer = "footer"
        )
        `when`(chatService.getCustomFormDetails(formId)).thenReturn(expectedResponse)
        val result = chatService.getCustomFormDetails(formId)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun submitCustomForm() = runBlocking {
        val request = SubmitCustomFormRequest(
            smartActionId = 123,
            formResponse = listOf(
                SubmitCustomFormRequest.Answer(questionId = 1, value = "Answer 1"),
                SubmitCustomFormRequest.Answer(questionId = 2, value = "Answer 2")
            )
        )
        `when`(chatService.submitCustomForm(request)).thenReturn(true)
        val result = chatService.submitCustomForm(request)
        assertTrue(result)
    }
}
