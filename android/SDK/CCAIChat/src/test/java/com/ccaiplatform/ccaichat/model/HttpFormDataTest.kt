package com.ccaiplatform.ccaichat.model

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class HttpFormDataTest {

    @Test
    fun contentTypeContainsBoundary() {
        val form = HttpFormData()
        assertTrue(form.contentType.startsWith("multipart/form-data; boundary="))
    }

    @Test
    fun appendSingleField() {
        val form = HttpFormData()
        form.append("field", "value")
        val data = String(form.dataRepresentation)
        assertTrue(data.contains("Content-Disposition: form-data; name=\"field\""))
        assertTrue(data.contains("value"))
    }

    @Test
    fun appendMultipleFields() {
        val form = HttpFormData()
        form.append(mapOf("a" to "1", "b" to "2"))
        val data = String(form.dataRepresentation)
        assertTrue(data.contains("name=\"a\""))
        assertTrue(data.contains("1"))
        assertTrue(data.contains("name=\"b\""))
        assertTrue(data.contains("2"))
    }

    @Test
    fun appendFileField() {
        val form = HttpFormData()
        val fileData = "filecontent".toByteArray()
        form.append("file", "test.txt", "text/plain", fileData)
        val data = String(form.dataRepresentation)
        assertTrue(data.contains("Content-Disposition: form-data; name=\"file\"; filename=\"test.txt\""))
        assertTrue(data.contains("Content-Type: text/plain"))
        assertTrue(data.contains("filecontent"))
    }

    @Test
    fun appendEmptyValueIsIgnored() {
        val form = HttpFormData()
        form.append("empty", "")
        val data = String(form.dataRepresentation)
        assertFalse(data.contains("name=\"empty\""))
    }

    @Test
    fun dataRepresentationIsFinalizedOnce() {
        val form = HttpFormData()
        form.append("foo", "bar")
        val first = form.dataRepresentation
        val second = form.dataRepresentation
        assertSame(first, second)
    }
}
