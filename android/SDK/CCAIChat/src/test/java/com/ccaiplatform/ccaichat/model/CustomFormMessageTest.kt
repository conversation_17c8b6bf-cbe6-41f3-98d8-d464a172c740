package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaikit.util.GsonProvider
import com.google.gson.JsonSyntaxException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNotSame
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class CustomFormMessageTest {

    @Test
    fun defaultConstructor() {
        val customFormMessage = CustomFormMessage()
        
        assertEquals(0, customFormMessage.id)
        assertEquals(0, customFormMessage.smartActionId)
        assertNull(customFormMessage.title)
        assertNull(customFormMessage.name)
    }

    @Test
    fun constructorWithParameters() {
        val customFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        assertEquals(123, customFormMessage.id)
        assertEquals(456, customFormMessage.smartActionId)
        assertEquals("Test Form", customFormMessage.title)
        assertEquals("test_form", customFormMessage.name)
    }

    @Test
    fun nullValues() {
        val customFormMessage = CustomFormMessage(
            id = null,
            smartActionId = null,
            title = null,
            name = null
        )
        
        assertNull(customFormMessage.id)
        assertNull(customFormMessage.smartActionId)
        assertNull(customFormMessage.title)
        assertNull(customFormMessage.name)
    }

    @Test
    fun emptyStrings() {
        val customFormMessage = CustomFormMessage(
            id = 0,
            smartActionId = 0,
            title = "",
            name = ""
        )
        
        assertEquals(0, customFormMessage.id)
        assertEquals(0, customFormMessage.smartActionId)
        assertEquals("", customFormMessage.title)
        assertEquals("", customFormMessage.name)
    }

    @Test
    fun negativeIds() {
        val customFormMessage = CustomFormMessage(
            id = -1,
            smartActionId = -2,
            title = "Test Form",
            name = "test_form"
        )
        
        assertEquals(-1, customFormMessage.id)
        assertEquals(-2, customFormMessage.smartActionId)
        assertEquals("Test Form", customFormMessage.title)
        assertEquals("test_form", customFormMessage.name)
    }

    @Test
    fun largeValues() {
        val customFormMessage = CustomFormMessage(
            id = Int.MAX_VALUE,
            smartActionId = Int.MAX_VALUE - 1,
            title = "b".repeat(500),
            name = "c".repeat(300)
        )
        
        assertEquals(Int.MAX_VALUE, customFormMessage.id)
        assertEquals(Int.MAX_VALUE - 1, customFormMessage.smartActionId)
        assertEquals("b".repeat(500), customFormMessage.title)
        assertEquals("c".repeat(300), customFormMessage.name)
    }

    @Test
    fun jsonSerialization() {
        val customFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val json = GsonProvider.gson.toJson(customFormMessage)
        
        assertNotNull(json)
        assertTrue(json.contains("\"id\":123"))
        assertTrue(json.contains("\"smart_action_id\":456"))
        assertTrue(json.contains("\"title\":\"Test Form\""))
        assertTrue(json.contains("\"name\":\"test_form\""))
    }

    @Test
    fun jsonSerializationWithNulls() {
        val customFormMessage = CustomFormMessage(
            id = null,
            smartActionId = null,
            title = null,
            name = null
        )
        
        val json = GsonProvider.gson.toJson(customFormMessage)
        
        assertNotNull(json)
        assertTrue(json.contains("{}") || json.contains("null"))
    }

    @Test
    fun jsonDeserialization() {
        val json = """
            {
                "id": 123,
                "smart_action_id": 456,
                "title": "Test Form",
                "name": "test_form"
            }
        """.trimIndent()
        
        val customFormMessage = GsonProvider.gson.fromJson(json, CustomFormMessage::class.java)
        
        assertNotNull(customFormMessage)
        assertEquals(123, customFormMessage.id)
        assertEquals(456, customFormMessage.smartActionId)
        assertEquals("Test Form", customFormMessage.title)
        assertEquals("test_form", customFormMessage.name)
    }

    @Test
    fun jsonDeserializationWithMissingFields() {
        val json = """
            {
                "id": 123
            }
        """.trimIndent()
        
        val customFormMessage = GsonProvider.gson.fromJson(json, CustomFormMessage::class.java)
        
        assertNotNull(customFormMessage)
        assertEquals(123, customFormMessage.id)
        assertEquals(0, customFormMessage.smartActionId)
        assertNull(customFormMessage.title)
        assertNull(customFormMessage.name)
    }

    @Test
    fun jsonDeserializationWithNulls() {
        val json = """
            {
                "id": null,
                "smart_action_id": null,
                "title": null,
                "name": null
            }
        """.trimIndent()
        
        val customFormMessage = GsonProvider.gson.fromJson(json, CustomFormMessage::class.java)
        
        assertNotNull(customFormMessage)
        assertNull(customFormMessage.id)
        assertNull(customFormMessage.smartActionId)
        assertNull(customFormMessage.title)
        assertNull(customFormMessage.name)
    }

    @Test
    fun jsonRoundTrip() {
        val originalCustomFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val json = GsonProvider.gson.toJson(originalCustomFormMessage)
        val deserializedCustomFormMessage = GsonProvider.gson.fromJson(json, CustomFormMessage::class.java)
        
        assertEquals(originalCustomFormMessage, deserializedCustomFormMessage)
    }

    @Test
    fun jsonDeserializationWithInvalidJson() {
        val invalidJson = "{ invalid json }"
        assertThrows(JsonSyntaxException::class.java) {
            GsonProvider.gson.fromJson(invalidJson, CustomFormMessage::class.java)
        }
    }

    @Test
    fun dataClassEquals() {
        val customFormMessage1 = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val customFormMessage2 = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val customFormMessage3 = CustomFormMessage(
            id = 456,
            smartActionId = 789,
            title = "Different Form",
            name = "different_form"
        )
        
        assertEquals(customFormMessage1, customFormMessage2)
        assertNotEquals(customFormMessage1, customFormMessage3)
        assertNotEquals(customFormMessage1, null)
        assertNotEquals(customFormMessage1, "not a CustomFormMessage")
    }

    @Test
    fun dataClassHashCode() {
        val customFormMessage1 = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val customFormMessage2 = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        assertEquals(customFormMessage1.hashCode(), customFormMessage2.hashCode())
    }

    @Test
    fun dataClassToString() {
        val customFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val toStringResult = customFormMessage.toString()
        
        assertNotNull(toStringResult)
        assertTrue(toStringResult.contains("CustomFormMessage"))
        assertTrue(toStringResult.contains("123"))
        assertTrue(toStringResult.contains("456"))
        assertTrue(toStringResult.contains("Test Form"))
        assertTrue(toStringResult.contains("test_form"))
    }

    @Test
    fun dataClassCopy() {
        val originalCustomFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val copiedCustomFormMessage = originalCustomFormMessage.copy()
        
        assertEquals(originalCustomFormMessage, copiedCustomFormMessage)
        assertNotSame(originalCustomFormMessage, copiedCustomFormMessage)
    }

    @Test
    fun dataClassCopyWithChanges() {
        val originalCustomFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val modifiedCustomFormMessage = originalCustomFormMessage.copy(
            id = 999,
            smartActionId = 789
        )
        
        assertEquals(999, modifiedCustomFormMessage.id)
        assertEquals(789, modifiedCustomFormMessage.smartActionId)
        assertEquals("Test Form", modifiedCustomFormMessage.title)
        assertEquals("test_form", modifiedCustomFormMessage.name)
    }

    @Test
    fun formMessageInterface() {
        val customFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        assertTrue(customFormMessage is FormMessage)
        assertEquals(456, customFormMessage.smartActionId)
        assertEquals("Test Form", customFormMessage.title)
        assertEquals("test_form", customFormMessage.name)
    }

    @Test
    fun formMessageInterfaceWithNulls() {
        val customFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = null,
            title = null,
            name = null
        )
        
        assertTrue(customFormMessage is FormMessage)
        assertNull(customFormMessage.smartActionId)
        assertNull(customFormMessage.title)
        assertNull(customFormMessage.name)
    }

    @Test
    fun specialCharacters() {
        val customFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form with special characters and symbols",
            name = "test_form_with_unicode_chars"
        )
        
        assertEquals(123, customFormMessage.id)
        assertEquals(456, customFormMessage.smartActionId)
        assertEquals("Test Form with special characters and symbols", customFormMessage.title)
        assertEquals("test_form_with_unicode_chars", customFormMessage.name)
    }

    @Test
    fun jsonSerializationWithSpecialCharacters() {
        val customFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form with special characters",
            name = "test_form_with_unicode_chars"
        )
        
        val json = GsonProvider.gson.toJson(customFormMessage)
        val deserializedCustomFormMessage = GsonProvider.gson.fromJson(json, CustomFormMessage::class.java)
        
        assertEquals(customFormMessage, deserializedCustomFormMessage)
    }

    @Test
    fun dataClassComponentFunctions() {
        val customFormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        val (id, smartActionId, title, name) = customFormMessage
        
        assertEquals(123, id)
        assertEquals(456, smartActionId)
        assertEquals("Test Form", title)
        assertEquals("test_form", name)
    }

    @Test
    fun zeroValues() {
        val customFormMessage = CustomFormMessage(
            id = 0,
            smartActionId = 0,
            title = null,
            name = null
        )
        
        assertEquals(0, customFormMessage.id)
        assertEquals(0, customFormMessage.smartActionId)
        assertNull(customFormMessage.title)
        assertNull(customFormMessage.name)
    }

    @Test
    fun minMaxValues() {
        val customFormMessage = CustomFormMessage(
            id = Int.MIN_VALUE,
            smartActionId = Int.MIN_VALUE,
            title = "Min Value Test",
            name = "min_value_test"
        )
        
        assertEquals(Int.MIN_VALUE, customFormMessage.id)
        assertEquals(Int.MIN_VALUE, customFormMessage.smartActionId)
        assertEquals("Min Value Test", customFormMessage.title)
        assertEquals("min_value_test", customFormMessage.name)
    }

    @Test
    fun formMessageInterfacePolymorphism() {
        val customFormMessage: FormMessage = CustomFormMessage(
            id = 123,
            smartActionId = 456,
            title = "Test Form",
            name = "test_form"
        )
        
        assertEquals(456, customFormMessage.smartActionId)
        assertEquals("Test Form", customFormMessage.title)
        assertEquals("test_form", customFormMessage.name)
    }

    @Test
    fun jsonDeserializationWithExtraFields() {
        val json = """
            {
                "id": 123,
                "smart_action_id": 456,
                "title": "Test Form",
                "name": "test_form",
                "extra_field": "should be ignored"
            }
        """.trimIndent()
        
        val customFormMessage = GsonProvider.gson.fromJson(json, CustomFormMessage::class.java)
        
        assertNotNull(customFormMessage)
        assertEquals(123, customFormMessage.id)
        assertEquals(456, customFormMessage.smartActionId)
        assertEquals("Test Form", customFormMessage.title)
        assertEquals("test_form", customFormMessage.name)
    }
}
