package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class ChatTransferEventTest {

    @Test
    fun startedEventWithIdentity() {
        val event = ChatTransferEvent.Started("user1")
        assertTrue(event is ChatTransferEvent.Started)
        assertEquals("user1", event.identity)
    }

    @Test
    fun startedEventWithNullIdentity() {
        val event = ChatTransferEvent.Started(null)
        assertTrue(event is ChatTransferEvent.Started)
        assertNull(event.identity)
    }

    @Test
    fun acceptedEventWithIdentity() {
        val event = ChatTransferEvent.Accepted("user2")
        assertTrue(event is ChatTransferEvent.Accepted)
        assertEquals("user2", event.identity)
    }

    @Test
    fun acceptedEventWithNullIdentity() {
        val event = ChatTransferEvent.Accepted(null)
        assertTrue(event is ChatTransferEvent.Accepted)
        assertNull(event.identity)
    }

    @Test
    fun failedEventWithIdentity() {
        val event = ChatTransferEvent.Failed("user3")
        assertTrue(event is ChatTransferEvent.Failed)
        assertEquals("user3", event.identity)
    }

    @Test
    fun failedEventWithNullIdentity() {
        val event = ChatTransferEvent.Failed(null)
        assertTrue(event is ChatTransferEvent.Failed)
        assertNull(event.identity)
    }

    @Test
    fun equalityChecks() {
        val started1 = ChatTransferEvent.Started("user")
        val started2 = ChatTransferEvent.Started("user")
        val started3 = ChatTransferEvent.Started(null)
        val started4 = ChatTransferEvent.Started(null)
        val accepted1 = ChatTransferEvent.Accepted("user")
        val accepted2 = ChatTransferEvent.Accepted("user")
        val failed1 = ChatTransferEvent.Failed("user")
        val failed2 = ChatTransferEvent.Failed("user")

        assertEquals(started1, started2)
        assertEquals(started3, started4)
        assertEquals(accepted1, accepted2)
        assertEquals(failed1, failed2)
        assertNotEquals(started1, started3)
        assertNotEquals(started1, accepted1)
        assertNotEquals(accepted1, failed1)
    }
}
