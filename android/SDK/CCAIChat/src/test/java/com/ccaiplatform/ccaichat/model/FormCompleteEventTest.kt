package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaikit.util.GsonProvider.gson
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class FormCompleteEventTest {

    @Test
    fun formCompleteEventSerializationAndDeserialization() {
        // Given
        val originalEvent = FormCompleteEvent(
            type = "form_complete",
            signature = "test_signature",
            data = FormCompleteEventData(
                status = "success",
                timestamp = "2024-03-20T10:00:00Z",
                smartActionId = 123,
                details = FormCompleteEventDetails(
                    errorCode = null,
                    message = null
                )
            )
        )

        // When
        val json = gson.toJson(originalEvent)
        val deserializedEvent = gson.fromJson(json, FormCompleteEvent::class.java)

        // Then
        assertEquals(originalEvent, deserializedEvent)
        assertEquals("form_complete", deserializedEvent.type)
        assertEquals("test_signature", deserializedEvent.signature)
        assertEquals("success", deserializedEvent.data?.status)
        assertEquals("2024-03-20T10:00:00Z", deserializedEvent.data?.timestamp)
        assertEquals(123, deserializedEvent.data?.smartActionId)
        assertNull(deserializedEvent.data?.details?.errorCode)
        assertNull(deserializedEvent.data?.details?.message)
    }

    @Test
    fun formCompleteEventWithErrorDetails() {
        // Given
        val originalEvent = FormCompleteEvent(
            type = "form_complete",
            signature = "test_signature",
            data = FormCompleteEventData(
                status = "error",
                timestamp = "2024-03-20T10:00:00Z",
                smartActionId = 123,
                details = FormCompleteEventDetails(
                    errorCode = "FORM_VALIDATION_ERROR",
                    message = "Invalid form data"
                )
            )
        )

        // When
        val json = gson.toJson(originalEvent)
        val deserializedEvent = gson.fromJson(json, FormCompleteEvent::class.java)

        // Then
        assertEquals(originalEvent, deserializedEvent)
        assertEquals("error", deserializedEvent.data?.status)
        assertEquals("FORM_VALIDATION_ERROR", deserializedEvent.data?.details?.errorCode)
        assertEquals("Invalid form data", deserializedEvent.data?.details?.message)
    }

    @Test
    fun formCompleteEventWithNullValues() {
        // Given
        val originalEvent = FormCompleteEvent(
            type = null,
            signature = null,
            data = null
        )

        // When
        val json = gson.toJson(originalEvent)
        val deserializedEvent = gson.fromJson(json, FormCompleteEvent::class.java)

        // Then
        assertEquals(originalEvent, deserializedEvent)
        assertNull(deserializedEvent.type)
        assertNull(deserializedEvent.signature)
        assertNull(deserializedEvent.data)
    }

    @Test
    fun formCompleteEventWithPartialData() {
        // Given
        val originalEvent = FormCompleteEvent(
            type = "form_complete",
            signature = "test_signature",
            data = FormCompleteEventData(
                status = "success",
                timestamp = null,
                smartActionId = null,
                details = null
            )
        )

        // When
        val json = gson.toJson(originalEvent)
        val deserializedEvent = gson.fromJson(json, FormCompleteEvent::class.java)

        // Then
        assertEquals(originalEvent, deserializedEvent)
        assertEquals("form_complete", deserializedEvent.type)
        assertEquals("test_signature", deserializedEvent.signature)
        assertEquals("success", deserializedEvent.data?.status)
        assertNull(deserializedEvent.data?.timestamp)
        assertNull(deserializedEvent.data?.smartActionId)
        assertNull(deserializedEvent.data?.details)
    }
} 
