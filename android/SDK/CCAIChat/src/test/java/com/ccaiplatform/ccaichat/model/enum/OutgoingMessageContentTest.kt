package com.ccaiplatform.ccaichat.model.enum

import android.net.Uri
import com.ccaiplatform.ccaichat.model.FormCompleteEventData
import com.ccaiplatform.ccaikit.models.SmartAction
import com.ccaiplatform.ccaikit.models.SmartActionStatus
import com.ccaiplatform.ccaikit.models.SmartActionType
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class OutgoingMessageContentTest {

    @Test
    fun textContentHoldsCorrectValue() {
        val text = OutgoingMessageContent.Text("hello")
        assertEquals("hello", text.content)
    }

    @Test
    fun photosContentHoldsAllProperties() {
        val photoBytes = listOf(byteArrayOf(1, 2, 3), byteArrayOf(4, 5, 6))
        val uris = listOf(Uri.parse("file://photo1"), Uri.parse("file://photo2"))
        val smartAction = SmartAction(1, SmartActionType.Photo, SmartActionStatus.Finished)
        val contentType = "image/jpeg"

        val photos = OutgoingMessageContent.Photos(
            photos = photoBytes,
            uris = uris,
            smartAction = smartAction,
            contentType = contentType
        )

        assertEquals(photoBytes, photos.photos)
        assertEquals(uris, photos.uris)
        assertEquals(smartAction, photos.smartAction)
        assertEquals(contentType, photos.contentType)
    }

    @Test
    fun photosContentAllowsNulls() {
        val photos = OutgoingMessageContent.Photos(
            photos = emptyList(),
            uris = emptyList(),
            smartAction = null,
            contentType = null
        )
        assertNull(photos.smartAction)
        assertNull(photos.contentType)
    }

    @Test
    fun formCompleteContentHoldsAllProperties() {
        val data = FormCompleteEventData(status = "completed", timestamp = "2024-06-01T12:00:00Z", smartActionId = 123, details = null)
        val form = OutgoingMessageContent.FormComplete(
            type = "survey",
            signature = "sig123",
            data = data
        )
        assertEquals("survey", form.type)
        assertEquals("sig123", form.signature)
        assertEquals(data, form.data)
    }

    @Test
    fun formCompleteContentAllowsNullTypeAndSignature() {
        val data = FormCompleteEventData(status = "completed", timestamp = "2024-06-01T12:00:00Z", smartActionId = 123, details = null)
        val form = OutgoingMessageContent.FormComplete(
            type = null,
            signature = null,
            data = data
        )
        assertNull(form.type)
        assertNull(form.signature)
        assertEquals(data, form.data)
    }
}
