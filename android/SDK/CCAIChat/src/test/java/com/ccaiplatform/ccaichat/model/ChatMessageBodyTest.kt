package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.ccaiplatform.ccaichat.model.enum.ChatMessageEvent
import com.ccaiplatform.ccaikit.models.response.communication.AgentStatus
import com.ccaiplatform.ccaikit.models.response.communication.HumanAgent
import com.ccaiplatform.ccaikit.util.GsonProvider
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class ChatMessageBodyTest {
    private val gson: Gson = GsonBuilder().create()

    @Test
    fun initialization() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Hello World")
        assertEquals(ChatMessageBodyType.Text, body.type)
        assertEquals("Hello World", body.content)

        val body2 = ChatMessageBody(type = ChatMessageBodyType.Image, content = "Hello World")
        assertEquals(ChatMessageBodyType.Image, body2.type)
        assertEquals("Hello World", body2.content)
    }

    @Test
    fun initializationWithNullContent() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = null)
        assertEquals(ChatMessageBodyType.Text, body.type)
        assertNull(body.content)
    }

    @Test
    fun decodingFromJSON() {
        val json = """
        {
            "type": "text",
            "content": "Hello World"
        }
        """.trimIndent()

        val body = GsonProvider.gson.fromJson(json, ChatMessageBody::class.java)

        assertEquals(ChatMessageBodyType.Text, body.type)
        assertEquals("Hello World", body.content)
    }

    @Test
    fun encodingToJSON() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Hello World")

        val jsonString = GsonProvider.gson.toJson(body)

        // Verify the JSON contains expected fields
        assert(jsonString.contains("\"type\":\"text\""))
        assert(jsonString.contains("\"content\":\"Hello World\""))
    }

    @Test
    fun encodingAndDecoding() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Hello World")
        val json = GsonProvider.gson.toJson(body)
        val decodedBody = GsonProvider.gson.fromJson(json, ChatMessageBody::class.java)

        assertEquals(body.type, decodedBody.type)
        assertEquals(body.content, decodedBody.content)
    }

    @Test
    fun encodingWithNullContent() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = null)
        val json = GsonProvider.gson.toJson(body)
        val decodedBody = GsonProvider.gson.fromJson(json, ChatMessageBody::class.java)

        assertEquals(body.type, decodedBody.type)
        assertNull(decodedBody.content)
    }

    @Test
    fun bodyTypeRawValues() {
        // Test all body types and their raw values
        assertEquals("unknown", ChatMessageBodyType.Unknown.value)
        assertEquals("text", ChatMessageBodyType.Text.value)
        assertEquals("photo", ChatMessageBodyType.Photo.value)
        assertEquals("video", ChatMessageBodyType.Video.value)
        assertEquals("noti", ChatMessageBodyType.Notification.value)
        assertEquals("text_template", ChatMessageBodyType.TextTemplate.value)
        assertEquals("markdown", ChatMessageBodyType.Markdown.value)
        assertEquals("markdown_template", ChatMessageBodyType.MarkdownTemplate.value)
        assertEquals("inline_button", ChatMessageBodyType.InlineButton.value)
        assertEquals("sticky_button", ChatMessageBodyType.StickyButton.value)
        assertEquals("document", ChatMessageBodyType.Document.value)
        assertEquals("image", ChatMessageBodyType.Image.value)
        assertEquals("content_card", ChatMessageBodyType.ContentCard.value)
        assertEquals("form", ChatMessageBodyType.Form.value)
        assertEquals("form_complete", ChatMessageBodyType.FormComplete.value)
        assertEquals("server_message", ChatMessageBodyType.ServerMessage.value)
    }

    @Test
    fun fullFieldSerializationDeserialization() {
        val agent = HumanAgent(
            id = 1,
            status = AgentStatus.Connected,
            name = "Human Agent",
            avatarUrl = "http://avatar.url",
            firstName = "First",
            lastName = "Last",
            email = "<EMAIL>"
        )
        val body = ChatMessageBody(
            type = ChatMessageBodyType.Text,
            event = ChatMessageEvent.None,
            content = "Test Content",
            escalationReason = "reason",
            name = "name",
            messageId = 42,
            reason = "some reason",
            formTitle = "form title",
            visibility = "public",
            agent = agent,
            escalationId = 123,
        )
        val json = GsonProvider.gson.toJson(body)
        val decoded = GsonProvider.gson.fromJson(json, ChatMessageBody::class.java)
        assertEquals(body.type, decoded.type)
        assertEquals(body.event, decoded.event)
        assertEquals(body.content, decoded.content)
        assertEquals(body.escalationReason, decoded.escalationReason)
        assertEquals(body.name, decoded.name)
        assertEquals(body.messageId, decoded.messageId)
        assertEquals(body.reason, decoded.reason)
        assertEquals(body.formTitle, decoded.formTitle)
        assertEquals(body.visibility, decoded.visibility)
        assertEquals(body.agent, decoded.agent)
        assertEquals(body.escalationId, decoded.escalationId)
    }

    @Test
    fun toJSONStringReturnsValidJson() {
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = "Hello World")
        val jsonString = body.toJSONString()
        assert(jsonString != null && jsonString.contains("\"type\":\"text\""))
    }

    @Test
    fun equalsAndHashCode() {
        val body1 = ChatMessageBody(type = ChatMessageBodyType.Text, content = "abc")
        val body2 = ChatMessageBody(type = ChatMessageBodyType.Text, content = "abc")
        assertEquals(body1, body2)
        assertEquals(body1.hashCode(), body2.hashCode())
    }

    @Test
    fun specialCharactersAndBoundaryValues() {
        val special = "!@#￥%……&*（）——+🙂🚀\"'"
        val body = ChatMessageBody(type = ChatMessageBodyType.Text, content = special, name = special)
        val json = GsonProvider.gson.toJson(body)
        val decoded = GsonProvider.gson.fromJson(json, ChatMessageBody::class.java)
        assertEquals(special, decoded.content)
        assertEquals(special, decoded.name)
    }

    @Test
    fun allFieldsNullOrDefault() {
        val body = ChatMessageBody()
        val json = GsonProvider.gson.toJson(body)
        val decoded = GsonProvider.gson.fromJson(json, ChatMessageBody::class.java)
        assertEquals(body, decoded)
    }
}
