package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaikit.util.GsonProvider
import com.google.gson.JsonSyntaxException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNotSame
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class CustomFormQuestionOptionTest {

    @Test
    fun customFormQuestionOptionDefaultConstructor() {
        val option = CustomFormQuestionOption()
        
        assertNull(option.id)
        assertNull(option.value)
        assertNull(option.image)
        assertNull(option.listPosition)
    }

    @Test
    fun customFormQuestionOptionConstructorWithParameters() {
        val option = CustomFormQuestionOption(
            id = 123,
            value = "Option Text",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        assertEquals(123, option.id)
        assertEquals("Option Text", option.value)
        assertEquals("https://example.com/image.png", option.image)
        assertEquals(1, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionNullValues() {
        val option = CustomFormQuestionOption(
            id = null,
            value = null,
            image = null,
            listPosition = null
        )
        
        assertNull(option.id)
        assertNull(option.value)
        assertNull(option.image)
        assertNull(option.listPosition)
    }

    @Test
    fun customFormQuestionOptionEmptyStrings() {
        val option = CustomFormQuestionOption(
            id = 0,
            value = "",
            image = "",
            listPosition = 0
        )
        
        assertEquals(0, option.id)
        assertEquals("", option.value)
        assertEquals("", option.image)
        assertEquals(0, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionNegativeValues() {
        val option = CustomFormQuestionOption(
            id = -1,
            listPosition = -5
        )
        
        assertEquals(-1, option.id)
        assertEquals(-5, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionLargeValues() {
        val option = CustomFormQuestionOption(
            id = Int.MAX_VALUE,
            value = "V".repeat(1000),
            image = "https://example.com/" + "path/".repeat(100) + "image.png",
            listPosition = Int.MAX_VALUE
        )
        
        assertEquals(Int.MAX_VALUE, option.id)
        assertEquals("V".repeat(1000), option.value)
        assertEquals("https://example.com/" + "path/".repeat(100) + "image.png", option.image)
        assertEquals(Int.MAX_VALUE, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionMinMaxIntegerValues() {
        val option = CustomFormQuestionOption(
            id = Int.MIN_VALUE,
            listPosition = Int.MAX_VALUE
        )
        
        assertEquals(Int.MIN_VALUE, option.id)
        assertEquals(Int.MAX_VALUE, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionZeroValues() {
        val option = CustomFormQuestionOption(
            id = 0,
            listPosition = 0
        )
        
        assertEquals(0, option.id)
        assertEquals(0, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionSpecialCharacters() {
        val option = CustomFormQuestionOption(
            value = "Option with special chars !@#$%^&*()",
            image = "https://example.com/path/with-special-chars_123.png"
        )
        
        assertEquals("Option with special chars !@#$%^&*()", option.value)
        assertEquals("https://example.com/path/with-special-chars_123.png", option.image)
    }

    @Test
    fun customFormQuestionOptionUrlFormats() {
        val httpOption = CustomFormQuestionOption(image = "http://example.com/image.jpg")
        val httpsOption = CustomFormQuestionOption(image = "https://example.com/image.png")
        val relativeOption = CustomFormQuestionOption(image = "/assets/image.gif")
        val dataUrlOption = CustomFormQuestionOption(image = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
        
        assertEquals("http://example.com/image.jpg", httpOption.image)
        assertEquals("https://example.com/image.png", httpsOption.image)
        assertEquals("/assets/image.gif", relativeOption.image)
        assertEquals("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==", dataUrlOption.image)
    }

    @Test
    fun customFormQuestionOptionJsonSerialization() {
        val option = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        val json = GsonProvider.gson.toJson(option)
        
        assertNotNull(json)
        assertTrue(json.contains("\"id\":123"))
        assertTrue(json.contains("\"value\":\"Test Option\""))
        assertTrue(json.contains("\"image\":\"https://example.com/image.png\""))
        assertTrue(json.contains("\"list_position\":1"))
    }

    @Test
    fun customFormQuestionOptionJsonSerializationWithNulls() {
        val option = CustomFormQuestionOption()
        
        val json = GsonProvider.gson.toJson(option)
        
        assertNotNull(json)
        assertTrue(json.contains("{}") || json.contains("null"))
    }

    @Test
    fun customFormQuestionOptionJsonDeserialization() {
        val json = """
            {
                "id": 123,
                "value": "Test Option",
                "image": "https://example.com/image.png",
                "list_position": 1
            }
        """.trimIndent()
        
        val option = GsonProvider.gson.fromJson(json, CustomFormQuestionOption::class.java)
        
        assertNotNull(option)
        assertEquals(123, option.id)
        assertEquals("Test Option", option.value)
        assertEquals("https://example.com/image.png", option.image)
        assertEquals(1, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionJsonDeserializationWithMissingFields() {
        val json = """
            {
                "id": 123,
                "value": "Test Option"
            }
        """.trimIndent()
        
        val option = GsonProvider.gson.fromJson(json, CustomFormQuestionOption::class.java)
        
        assertNotNull(option)
        assertEquals(123, option.id)
        assertEquals("Test Option", option.value)
        assertNull(option.image)
        assertNull(option.listPosition)
    }

    @Test
    fun customFormQuestionOptionJsonDeserializationWithNulls() {
        val json = """
            {
                "id": null,
                "value": null,
                "image": null,
                "list_position": null
            }
        """.trimIndent()
        
        val option = GsonProvider.gson.fromJson(json, CustomFormQuestionOption::class.java)
        
        assertNotNull(option)
        assertNull(option.id)
        assertNull(option.value)
        assertNull(option.image)
        assertNull(option.listPosition)
    }

    @Test
    fun customFormQuestionOptionJsonRoundTrip() {
        val originalOption = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        val json = GsonProvider.gson.toJson(originalOption)
        val deserializedOption = GsonProvider.gson.fromJson(json, CustomFormQuestionOption::class.java)
        
        assertEquals(originalOption, deserializedOption)
    }

    @Test
    fun customFormQuestionOptionJsonDeserializationWithExtraFields() {
        val json = """
            {
                "id": 123,
                "value": "Test Option",
                "image": "https://example.com/image.png",
                "list_position": 1,
                "extra_field": "should be ignored",
                "another_field": 456
            }
        """.trimIndent()
        
        val option = GsonProvider.gson.fromJson(json, CustomFormQuestionOption::class.java)
        
        assertNotNull(option)
        assertEquals(123, option.id)
        assertEquals("Test Option", option.value)
        assertEquals("https://example.com/image.png", option.image)
        assertEquals(1, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionJsonDeserializationWithInvalidJson() {
        val invalidJson = "{ invalid json }"
        assertThrows(JsonSyntaxException::class.java) {
            GsonProvider.gson.fromJson(invalidJson, CustomFormQuestionOption::class.java)
        }
    }

    @Test
    fun customFormQuestionOptionEquals() {
        val option1 = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        val option2 = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        val option3 = CustomFormQuestionOption(
            id = 456,
            value = "Different Option",
            image = "https://example.com/different.png",
            listPosition = 2
        )
        
        assertEquals(option1, option2)
        assertNotEquals(option1, option3)
        assertNotEquals(option1, null)
        assertNotEquals(option1, "not a CustomFormQuestionOption")
    }

    @Test
    fun customFormQuestionOptionHashCode() {
        val option1 = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        val option2 = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        assertEquals(option1.hashCode(), option2.hashCode())
    }

    @Test
    fun customFormQuestionOptionToString() {
        val option = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        val toStringResult = option.toString()
        
        assertNotNull(toStringResult)
        assertTrue(toStringResult.contains("CustomFormQuestionOption"))
        assertTrue(toStringResult.contains("123"))
        assertTrue(toStringResult.contains("Test Option"))
        assertTrue(toStringResult.contains("https://example.com/image.png"))
        assertTrue(toStringResult.contains("1"))
    }

    @Test
    fun customFormQuestionOptionCopy() {
        val originalOption = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        val copiedOption = originalOption.copy()
        
        assertEquals(originalOption, copiedOption)
        assertNotSame(originalOption, copiedOption)
    }

    @Test
    fun customFormQuestionOptionCopyWithChanges() {
        val originalOption = CustomFormQuestionOption(
            id = 123,
            value = "Original Option",
            image = "https://example.com/original.png",
            listPosition = 1
        )
        
        val modifiedOption = originalOption.copy(
            id = 456,
            value = "Modified Option"
        )
        
        assertEquals(456, modifiedOption.id)
        assertEquals("Modified Option", modifiedOption.value)
        assertEquals("https://example.com/original.png", modifiedOption.image)
        assertEquals(1, modifiedOption.listPosition)
    }

    @Test
    fun customFormQuestionOptionComponentFunctions() {
        val option = CustomFormQuestionOption(
            id = 123,
            value = "Test Option",
            image = "https://example.com/image.png",
            listPosition = 1
        )
        
        val (id, value, image, listPosition) = option
        
        assertEquals(123, id)
        assertEquals("Test Option", value)
        assertEquals("https://example.com/image.png", image)
        assertEquals(1, listPosition)
    }

    @Test
    fun customFormQuestionOptionLongValueText() {
        val longValue = "This is a very long option value that might be used in a form. ".repeat(20)
        val option = CustomFormQuestionOption(
            id = 1,
            value = longValue,
            listPosition = 1
        )
        
        assertEquals(1, option.id)
        assertEquals(longValue, option.value)
        assertEquals(1, option.listPosition)
    }

    @Test
    fun customFormQuestionOptionEdgeCasePositions() {
        val firstOption = CustomFormQuestionOption(id = 1, listPosition = 0)
        val lastOption = CustomFormQuestionOption(id = 2, listPosition = 999)
        val negativeOption = CustomFormQuestionOption(id = 3, listPosition = -1)
        
        assertEquals(0, firstOption.listPosition)
        assertEquals(999, lastOption.listPosition)
        assertEquals(-1, negativeOption.listPosition)
    }
}
