package com.ccaiplatform.ccaichat.model

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ContentCardTest {

    @Test
    fun shouldCreateContentCard() {
        val card = ContentCard(
            title = "Card Title",
            subtitle = "Card Subtitle",
            body = "Card Body",
            link = "http://example.com",
            eventParams = mapOf("key" to "value"),
            buttons = listOf(ContentCardButton(title = "Button Title", link = "http://example.com/button")),
            images = listOf("http://example.com/image.jpg")
        )
        assertEquals("Card Title", card.title)
        assertEquals("Card Subtitle", card.subtitle)
        assertEquals("Card Body", card.body)
        assertEquals("http://example.com", card.link)
        assertEquals(mapOf("key" to "value"), card.eventParams)
        assertEquals(1, card.buttons?.size)
        assertEquals("Button Title", card.buttons?.get(0)?.title)
        assertEquals("http://example.com/button", card.buttons?.get(0)?.link)
        assertEquals(1, card.images?.size)
        assertEquals("http://example.com/image.jpg", card.images?.get(0))
    }

    @Test
    fun shouldCreateContentCardWithNullFields() {
        val card = ContentCard(
            title = null,
            subtitle = null,
            body = null,
            link = null,
            eventParams = null,
            buttons = null,
            images = null
        )
        assertEquals(null, card.title)
        assertEquals(null, card.subtitle)
        assertEquals(null, card.body)
        assertEquals(null, card.link)
        assertEquals(null, card.eventParams)
        assertEquals(null, card.buttons)
        assertEquals(null, card.images)
    }

    @Test
    fun defaultConstructor() {
        val card = ContentCard()
        assertEquals(null, card.title)
        assertEquals(null, card.subtitle)
        assertEquals(null, card.body)
        assertEquals(null, card.link)
        assertEquals(null, card.eventParams)
        assertEquals(null, card.buttons)
        assertEquals(null, card.images)
    }

    @Test
    fun partiallyPopulatedConstructor() {
        val card = ContentCard(
            title = "Only Title"
        )
        assertEquals("Only Title", card.title)
        assertEquals(null, card.subtitle)
        assertEquals(null, card.body)
        assertEquals(null, card.link)
        assertEquals(null, card.eventParams)
        assertEquals(null, card.buttons)
        assertEquals(null, card.images)
    }

    @Test
    fun withTitleAndSubtitleOnly() {
        val card = ContentCard(
            title = "Title",
            subtitle = "Subtitle"
        )
        assertEquals("Title", card.title)
        assertEquals("Subtitle", card.subtitle)
        assertEquals(null, card.body)
        assertEquals(null, card.link)
        assertEquals(null, card.eventParams)
        assertEquals(null, card.buttons)
        assertEquals(null, card.images)
    }

    @Test
    fun withEmptyCollections() {
        val card = ContentCard(
            title = "Title",
            eventParams = emptyMap(),
            buttons = emptyList(),
            images = emptyList()
        )
        assertEquals("Title", card.title)
        assertEquals(emptyMap<String, String>(), card.eventParams)
        assertEquals(emptyList<ContentCardButton>(), card.buttons)
        assertEquals(emptyList<String>(), card.images)
        assertEquals(0, card.buttons?.size)
        assertEquals(0, card.images?.size)
    }

    @Test
    fun withMultipleButtons() {
        val button1 = ContentCardButton(title = "Button 1", link = "http://example.com/1")
        val button2 = ContentCardButton(title = "Button 2", link = "http://example.com/2")
        val buttons = listOf(button1, button2)
        
        val card = ContentCard(
            title = "Card with Multiple Buttons",
            buttons = buttons
        )
        
        assertEquals("Card with Multiple Buttons", card.title)
        assertEquals(2, card.buttons?.size)
        assertEquals("Button 1", card.buttons?.get(0)?.title)
        assertEquals("Button 2", card.buttons?.get(1)?.title)
    }

    @Test
    fun withMultipleImages() {
        val images = listOf(
            "http://example.com/image1.jpg",
            "http://example.com/image2.png",
            "http://example.com/image3.gif"
        )
        
        val card = ContentCard(
            title = "Card with Multiple Images",
            images = images
        )
        
        assertEquals("Card with Multiple Images", card.title)
        assertEquals(3, card.images?.size)
        assertEquals("http://example.com/image1.jpg", card.images?.get(0))
        assertEquals("http://example.com/image2.png", card.images?.get(1))
        assertEquals("http://example.com/image3.gif", card.images?.get(2))
    }

    @Test
    fun withComplexEventParams() {
        val eventParams = mapOf(
            "action" to "click",
            "category" to "content_card",
            "label" to "test_card",
            "value" to "123"
        )
        
        val card = ContentCard(
            title = "Card with Event Params",
            eventParams = eventParams
        )
        
        assertEquals("Card with Event Params", card.title)
        assertEquals(4, card.eventParams?.size)
        assertEquals("click", card.eventParams?.get("action"))
        assertEquals("content_card", card.eventParams?.get("category"))
        assertEquals("test_card", card.eventParams?.get("label"))
        assertEquals("123", card.eventParams?.get("value"))
    }

    @Test
    fun withSpecialCharacters() {
        val card = ContentCard(
            title = "Title with special chars & symbols!",
            subtitle = "Subtitle with emojis and accents",
            body = "Body with\nnew lines\tand tabs",
            link = "https://example.com/path?param=value&other=123"
        )
        
        assertEquals("Title with special chars & symbols!", card.title)
        assertEquals("Subtitle with emojis and accents", card.subtitle)
        assertEquals("Body with\nnew lines\tand tabs", card.body)
        assertEquals("https://example.com/path?param=value&other=123", card.link)
    }

    @Test
    fun withLongContent() {
        val longTitle = "Very ".repeat(100) + "Long Title"
        val longBody = "Lorem ipsum ".repeat(200) + "dolor sit amet"
        
        val card = ContentCard(
            title = longTitle,
            body = longBody
        )
        
        assertEquals(longTitle, card.title)
        assertEquals(longBody, card.body)
        assertTrue(card.title!!.length > 500)
        assertTrue(card.body!!.length > 1000)
    }

    @Test
    fun dataClassEquality() {
        val card1 = ContentCard(
            title = "Same Title",
            subtitle = "Same Subtitle",
            body = "Same Body"
        )
        
        val card2 = ContentCard(
            title = "Same Title",
            subtitle = "Same Subtitle",
            body = "Same Body"
        )
        
        assertEquals(card1, card2)
        assertEquals(card1.hashCode(), card2.hashCode())
    }

    @Test
    fun dataClassCopy() {
        val original = ContentCard(
            title = "Original Title",
            subtitle = "Original Subtitle"
        )
        
        val copied = original.copy(title = "Modified Title")
        
        assertEquals("Modified Title", copied.title)
        assertEquals("Original Subtitle", copied.subtitle)
        assertEquals(original.subtitle, copied.subtitle)
    }

    @Test
    fun dataClassToString() {
        val card = ContentCard(
            title = "Test Title",
            subtitle = "Test Subtitle"
        )
        
        val toString = card.toString()
        assertNotNull(toString)
        assertTrue(toString.contains("ContentCard"))
        assertTrue(toString.contains("Test Title"))
        assertTrue(toString.contains("Test Subtitle"))
    }

    @Test
    fun dataClassDestructuring() {
        val card = ContentCard(
            title = "Title",
            subtitle = "Subtitle",
            body = "Body",
            link = "Link",
            eventParams = mapOf("key" to "value"),
            buttons = listOf(ContentCardButton()),
            images = listOf("image.jpg")
        )
        
        val (title, subtitle, body, link, eventParams, buttons, images) = card
        assertEquals("Title", title)
        assertEquals("Subtitle", subtitle)
        assertEquals("Body", body)
        assertEquals("Link", link)
        assertEquals(mapOf("key" to "value"), eventParams)
        assertEquals(1, buttons?.size)
        assertEquals(1, images?.size)
    }
}
