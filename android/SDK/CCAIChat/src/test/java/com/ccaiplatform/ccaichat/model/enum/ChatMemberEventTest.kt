package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class ChatMemberEventTest {
    @Test
    fun initialization() {
        // Test Joined event
        val joinedEvent = ChatMemberEvent.Joined("user123")
        assertEquals("user123", joinedEvent.identity)
        // Test Left event
        val leftEvent = ChatMemberEvent.Left("user123")
        assertEquals("user123", leftEvent.identity)
    }

    @Test
    fun initializationWithNullIdentity() {
        // Test Joined event with null identity
        val joinedEvent = ChatMemberEvent.Joined(null)
        assertNull(joinedEvent.identity)

        // Test Left event with null identity
        val leftEvent = ChatMemberEvent.Left(null)
        assertNull(leftEvent.identity)
    }

    @Test
    fun equality() {
        val joinedEvent1 = ChatMemberEvent.Joined("user123")
        val joinedEvent2 = ChatMemberEvent.Joined("user123")
        val joinedEvent3 = ChatMemberEvent.Joined("user456")

        assertEquals(joinedEvent1, joinedEvent2)
        assert(joinedEvent1 != joinedEvent3)
    }

    @Test
    fun equalityWithNullIdentity() {
        val joinedEvent1 = ChatMemberEvent.Joined(null)
        val joinedEvent2 = ChatMemberEvent.Joined(null)
        val leftEvent1 = ChatMemberEvent.Left(null)
        val leftEvent2 = ChatMemberEvent.Left(null)

        assertEquals(joinedEvent1, joinedEvent2)
        assertEquals(leftEvent1, leftEvent2)
    }
}
