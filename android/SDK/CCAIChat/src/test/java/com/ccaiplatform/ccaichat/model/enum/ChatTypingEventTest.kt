package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ChatTypingEventTest {

    @Test
    fun startedEvent() {
        val identity = "user123"
        val event = ChatTypingEvent.Started(identity)
        assertTrue(event is ChatTypingEvent.Started)
        assertEquals(identity, event.identity)
    }

    @Test
    fun startedEventWithNullIdentity() {
        val event = ChatTypingEvent.Started(null)
        assertTrue(event is ChatTypingEvent.Started)
        assertNull(event.identity)
    }

    @Test
    fun endedEvent() {
        val identity = "user123"
        val event = ChatTypingEvent.Ended(identity)
        assertTrue(event is ChatTypingEvent.Ended)
        assertEquals(identity, event.identity)
    }

    @Test
    fun endedEventWithNullIdentity() {
        val event = ChatTypingEvent.Ended(null)
        assertTrue(event is ChatTypingEvent.Ended)
        assertNull(event.identity)
    }

    @Test
    fun equality() {
        // Test Started events
        val started1 = ChatTypingEvent.Started("user123")
        val started2 = ChatTypingEvent.Started("user123")
        val started3 = ChatTypingEvent.Started("user456")
        val started4 = ChatTypingEvent.Started(null)
        val started5 = ChatTypingEvent.Started(null)

        assertEquals(started1, started2)
        assertFalse(started1 == started3)
        assertFalse(started1 == started4)
        assertEquals(started4, started5)

        // Test Ended events
        val ended1 = ChatTypingEvent.Ended("user123")
        val ended2 = ChatTypingEvent.Ended("user123")
        val ended3 = ChatTypingEvent.Ended("user456")
        val ended4 = ChatTypingEvent.Ended(null)
        val ended5 = ChatTypingEvent.Ended(null)

        assertEquals(ended1, ended2)
        assertFalse(ended1 == ended3)
        assertFalse(ended1 == ended4)
        assertEquals(ended4, ended5)
    }
}
