package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaikit.util.GsonProvider
import com.google.gson.JsonSyntaxException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNotSame
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ButtonMessageTest {

    @Test
    fun defaultConstructor() {
        val buttonMessage = ButtonMessage()
        
        assertNull(buttonMessage.title)
        assertNull(buttonMessage.action)
        assertNull(buttonMessage.link)
        assertNull(buttonMessage.eventParams)
        assertNull(buttonMessage.escalationId)
    }

    @Test
    fun constructorWithParameters() {
        val eventParams = mapOf(
            "param1" to "value1",
            "param2" to 123,
            "param3" to true
        )
        
        val buttonMessage = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            link = "https://example.com",
            eventParams = eventParams,
            escalationId = 456
        )
        
        assertEquals("Test Button", buttonMessage.title)
        assertEquals(ButtonAction.QuickReply, buttonMessage.action)
        assertEquals("https://example.com", buttonMessage.link)
        assertEquals(eventParams, buttonMessage.eventParams)
        assertEquals(456, buttonMessage.escalationId)
    }

    @Test
    fun nullValues() {
        val buttonMessage = ButtonMessage(
            title = null,
            action = null,
            link = null,
            eventParams = null,
            escalationId = null
        )
        
        assertNull(buttonMessage.title)
        assertNull(buttonMessage.action)
        assertNull(buttonMessage.link)
        assertNull(buttonMessage.eventParams)
        assertNull(buttonMessage.escalationId)
    }

    @Test
    fun emptyStrings() {
        val buttonMessage = ButtonMessage(
            title = "",
            link = "",
            escalationId = 0
        )
        
        assertEquals("", buttonMessage.title)
        assertEquals("", buttonMessage.link)
        assertEquals(0, buttonMessage.escalationId)
    }

    @Test
    fun differentButtonActions() {
        val quickReplyButton = ButtonMessage(action = ButtonAction.QuickReply)
        val escalationButton = ButtonMessage(action = ButtonAction.Escalation)
        val linkButton = ButtonMessage(action = ButtonAction.QuickReplyLink)
        val externalLinkButton = ButtonMessage(action = ButtonAction.ExternalDeflectionLink)
        val virtualAgentButton = ButtonMessage(action = ButtonAction.ContinueVirtualAgent)
        val emailButton = ButtonMessage(action = ButtonAction.Email)
        val endChatButton = ButtonMessage(action = ButtonAction.EndChat)
        
        assertEquals(ButtonAction.QuickReply, quickReplyButton.action)
        assertEquals(ButtonAction.Escalation, escalationButton.action)
        assertEquals(ButtonAction.QuickReplyLink, linkButton.action)
        assertEquals(ButtonAction.ExternalDeflectionLink, externalLinkButton.action)
        assertEquals(ButtonAction.ContinueVirtualAgent, virtualAgentButton.action)
        assertEquals(ButtonAction.Email, emailButton.action)
        assertEquals(ButtonAction.EndChat, endChatButton.action)
    }

    @Test
    fun negativeEscalationId() {
        val buttonMessage = ButtonMessage(escalationId = -1)
        
        assertEquals(-1, buttonMessage.escalationId)
    }

    @Test
    fun largeEscalationId() {
        val buttonMessage = ButtonMessage(escalationId = Int.MAX_VALUE)
        
        assertEquals(Int.MAX_VALUE, buttonMessage.escalationId)
    }

    @Test
    fun minEscalationId() {
        val buttonMessage = ButtonMessage(escalationId = Int.MIN_VALUE)
        
        assertEquals(Int.MIN_VALUE, buttonMessage.escalationId)
    }

    @Test
    fun longTitle() {
        val longTitle = "Very long button title ".repeat(100)
        val buttonMessage = ButtonMessage(title = longTitle)
        
        assertEquals(longTitle, buttonMessage.title)
    }

    @Test
    fun specialCharacters() {
        val buttonMessage = ButtonMessage(
            title = "Button with special chars !@#$%^&*()",
            link = "https://example.com/path?param=value&special=!@#"
        )
        
        assertEquals("Button with special chars !@#$%^&*()", buttonMessage.title)
        assertEquals("https://example.com/path?param=value&special=!@#", buttonMessage.link)
    }

    @Test
    fun differentLinkFormats() {
        val httpButton = ButtonMessage(link = "http://example.com")
        val httpsButton = ButtonMessage(link = "https://example.com")
        val relativeButton = ButtonMessage(link = "/relative/path")
        val anchorButton = ButtonMessage(link = "#anchor")
        val mailtoButton = ButtonMessage(link = "mailto:<EMAIL>")
        val telButton = ButtonMessage(link = "tel:+1234567890")
        
        assertEquals("http://example.com", httpButton.link)
        assertEquals("https://example.com", httpsButton.link)
        assertEquals("/relative/path", relativeButton.link)
        assertEquals("#anchor", anchorButton.link)
        assertEquals("mailto:<EMAIL>", mailtoButton.link)
        assertEquals("tel:+1234567890", telButton.link)
    }

    @Test
    fun emptyEventParams() {
        val buttonMessage = ButtonMessage(eventParams = emptyMap())
        
        assertNotNull(buttonMessage.eventParams)
        assertTrue(buttonMessage.eventParams!!.isEmpty())
    }

    @Test
    fun complexEventParams() {
        val eventParams = mapOf(
            "string_param" to "test_value",
            "int_param" to 123,
            "boolean_param" to true,
            "double_param" to 45.67,
            "nested_map" to mapOf("inner_key" to "inner_value"),
            "list_param" to listOf("item1", "item2", "item3")
        )
        
        val buttonMessage = ButtonMessage(eventParams = eventParams)
        
        assertEquals(eventParams, buttonMessage.eventParams)
        assertEquals("test_value", buttonMessage.eventParams!!["string_param"])
        assertEquals(123, buttonMessage.eventParams!!["int_param"])
        assertEquals(true, buttonMessage.eventParams!!["boolean_param"])
        assertEquals(45.67, buttonMessage.eventParams!!["double_param"])
    }

    @Test
    fun jsonSerialization() {
        val buttonMessage = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            link = "https://example.com",
            escalationId = 123
        )
        
        val json = GsonProvider.gson.toJson(buttonMessage)
        
        assertNotNull(json)
        assertTrue(json.contains("\"title\":\"Test Button\""))
        assertTrue(json.contains("\"action\":\"quick_reply\""))
        assertTrue(json.contains("\"link\":\"https://example.com\""))
        assertTrue(json.contains("\"escalationId\":123"))
    }

    @Test
    fun jsonSerializationWithEventParams() {
        val eventParams = mapOf(
            "param1" to "value1",
            "param2" to 123
        )
        
        val buttonMessage = ButtonMessage(
            title = "Test Button",
            eventParams = eventParams
        )
        
        val json = GsonProvider.gson.toJson(buttonMessage)
        
        assertNotNull(json)
        assertTrue(json.contains("\"title\":\"Test Button\""))
        assertTrue(json.contains("\"event_params\""))
        assertTrue(json.contains("\"param1\":\"value1\""))
        assertTrue(json.contains("\"param2\":123"))
    }

    @Test
    fun jsonSerializationWithNulls() {
        val buttonMessage = ButtonMessage()
        
        val json = GsonProvider.gson.toJson(buttonMessage)
        
        assertNotNull(json)
        assertTrue(json.contains("{}") || json.contains("null"))
    }

    @Test
    fun jsonDeserialization() {
        val json = """
            {
                "title": "Test Button",
                "action": "escalation",
                "link": "https://example.com",
                "event_params": {
                    "param1": "value1",
                    "param2": 123
                },
                "escalationId": 456
            }
        """.trimIndent()
        
        val buttonMessage = GsonProvider.gson.fromJson(json, ButtonMessage::class.java)
        
        assertNotNull(buttonMessage)
        assertEquals("Test Button", buttonMessage.title)
        assertEquals(ButtonAction.Escalation, buttonMessage.action)
        assertEquals("https://example.com", buttonMessage.link)
        assertEquals(456, buttonMessage.escalationId)
        assertNotNull(buttonMessage.eventParams)
        assertEquals("value1", buttonMessage.eventParams!!["param1"])
        assertEquals(123.0, buttonMessage.eventParams!!["param2"]) // Gson converts numbers to Double by default
    }

    @Test
    fun jsonDeserializationWithMissingFields() {
        val json = """
            {
                "title": "Test Button"
            }
        """.trimIndent()
        
        val buttonMessage = GsonProvider.gson.fromJson(json, ButtonMessage::class.java)
        
        assertNotNull(buttonMessage)
        assertEquals("Test Button", buttonMessage.title)
        assertNull(buttonMessage.action)
        assertNull(buttonMessage.link)
        assertNull(buttonMessage.eventParams)
        assertNull(buttonMessage.escalationId)
    }

    @Test
    fun jsonDeserializationWithNulls() {
        val json = """
            {
                "title": null,
                "action": null,
                "link": null,
                "event_params": null,
                "escalationId": null
            }
        """.trimIndent()
        
        val buttonMessage = GsonProvider.gson.fromJson(json, ButtonMessage::class.java)
        
        assertNotNull(buttonMessage)
        assertNull(buttonMessage.title)
        assertNull(buttonMessage.action)
        assertNull(buttonMessage.link)
        assertNull(buttonMessage.eventParams)
        assertNull(buttonMessage.escalationId)
    }

    @Test
    fun jsonRoundTrip() {
        val originalButton = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReplyLink,
            link = "https://example.com",
            escalationId = 789
        )
        
        val json = GsonProvider.gson.toJson(originalButton)
        val deserializedButton = GsonProvider.gson.fromJson(json, ButtonMessage::class.java)
        
        assertEquals(originalButton, deserializedButton)
    }

    @Test
    fun jsonDeserializationWithExtraFields() {
        val json = """
            {
                "title": "Test Button",
                "action": "quick_reply",
                "link": "https://example.com",
                "escalationId": 123,
                "extra_field": "should be ignored",
                "another_field": 456
            }
        """.trimIndent()
        
        val buttonMessage = GsonProvider.gson.fromJson(json, ButtonMessage::class.java)
        
        assertNotNull(buttonMessage)
        assertEquals("Test Button", buttonMessage.title)
        assertEquals(ButtonAction.QuickReply, buttonMessage.action)
        assertEquals("https://example.com", buttonMessage.link)
        assertEquals(123, buttonMessage.escalationId)
    }

    @Test
    fun jsonDeserializationWithInvalidJson() {
        val invalidJson = "{ invalid json }"
        assertThrows(JsonSyntaxException::class.java) {
            GsonProvider.gson.fromJson(invalidJson, ButtonMessage::class.java)
        }
    }

    @Test
    fun jsonDeserializationWithInvalidAction() {
        val json = """
            {
                "title": "Test Button",
                "action": "invalid_action"
            }
        """.trimIndent()
        
        val buttonMessage = GsonProvider.gson.fromJson(json, ButtonMessage::class.java)
        
        assertNotNull(buttonMessage)
        assertEquals("Test Button", buttonMessage.title)
        assertEquals(ButtonAction.QuickReply, buttonMessage.action) // Should fallback to default
    }

    @Test
    fun dataClassEquals() {
        val eventParams = mapOf("param" to "value")
        
        val button1 = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            link = "https://example.com",
            eventParams = eventParams,
            escalationId = 123
        )
        
        val button2 = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            link = "https://example.com",
            eventParams = eventParams,
            escalationId = 123
        )
        
        val button3 = ButtonMessage(
            title = "Different Button",
            action = ButtonAction.Escalation,
            link = "https://different.com",
            escalationId = 456
        )
        
        assertEquals(button1, button2)
        assertNotEquals(button1, button3)
        assertNotEquals(button1, null)
        assertNotEquals(button1, "not a ButtonMessage")
    }

    @Test
    fun dataClassHashCode() {
        val eventParams = mapOf("param" to "value")
        
        val button1 = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            eventParams = eventParams
        )
        
        val button2 = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            eventParams = eventParams
        )
        
        assertEquals(button1.hashCode(), button2.hashCode())
    }

    @Test
    fun dataClassToString() {
        val button = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            link = "https://example.com",
            escalationId = 123
        )
        
        val toStringResult = button.toString()
        
        assertNotNull(toStringResult)
        assertTrue(toStringResult.contains("ButtonMessage"))
        assertTrue(toStringResult.contains("Test Button"))
        assertTrue(toStringResult.contains("QuickReply"))
        assertTrue(toStringResult.contains("https://example.com"))
        assertTrue(toStringResult.contains("123"))
    }

    @Test
    fun dataClassCopy() {
        val originalButton = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            link = "https://example.com"
        )
        
        val copiedButton = originalButton.copy()
        
        assertEquals(originalButton, copiedButton)
        assertNotSame(originalButton, copiedButton)
    }

    @Test
    fun dataClassCopyWithChanges() {
        val originalButton = ButtonMessage(
            title = "Original Button",
            action = ButtonAction.QuickReply,
            link = "https://example.com",
            escalationId = 123
        )
        
        val modifiedButton = originalButton.copy(
            title = "Modified Button",
            action = ButtonAction.Escalation
        )
        
        assertEquals("Modified Button", modifiedButton.title)
        assertEquals(ButtonAction.Escalation, modifiedButton.action)
        assertEquals("https://example.com", modifiedButton.link)
        assertEquals(123, modifiedButton.escalationId)
    }

    @Test
    fun dataClassComponentFunctions() {
        val eventParams = mapOf("param" to "value")
        
        val button = ButtonMessage(
            title = "Test Button",
            action = ButtonAction.QuickReply,
            link = "https://example.com",
            eventParams = eventParams,
            escalationId = 123
        )
        
        val (title, action, link, params, escalationId) = button
        
        assertEquals("Test Button", title)
        assertEquals(ButtonAction.QuickReply, action)
        assertEquals("https://example.com", link)
        assertEquals(eventParams, params)
        assertEquals(123, escalationId)
    }

    @Test
    fun zeroEscalationId() {
        val button = ButtonMessage(escalationId = 0)
        
        assertEquals(0, button.escalationId)
    }

    @Test
    fun allActionsJsonSerialization() {
        val actions = listOf(
            ButtonAction.QuickReply,
            ButtonAction.Escalation,
            ButtonAction.QuickReplyLink,
            ButtonAction.ExternalDeflectionLink,
            ButtonAction.ContinueVirtualAgent,
            ButtonAction.Email,
            ButtonAction.EndChat
        )
        
        actions.forEach { action ->
            val button = ButtonMessage(title = "Test", action = action)
            val json = GsonProvider.gson.toJson(button)
            val deserializedButton = GsonProvider.gson.fromJson(json, ButtonMessage::class.java)
            
            assertEquals(action, deserializedButton.action)
        }
    }
}
