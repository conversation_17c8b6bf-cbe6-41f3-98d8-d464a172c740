package com.ccaiplatform.ccaichat.model

import android.os.Parcel
import com.ccaiplatform.ccaikit.util.GsonProvider
import com.google.gson.JsonSyntaxException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNotSame
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify

class MenuTest {

    @Test
    fun menuDefaultConstructor() {
        val menu = Menu()
        
        assertEquals(-1, menu.id)
        assertNull(menu.name)
    }

    @Test
    fun menuConstructorWithParameters() {
        val menu = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        assertEquals(123, menu.id)
        assertEquals("Test Menu", menu.name)
    }

    @Test
    fun menuConstructorWithNullName() {
        val menu = Menu(
            id = 456,
            name = null
        )
        
        assertEquals(456, menu.id)
        assertNull(menu.name)
    }

    @Test
    fun menuEmptyName() {
        val menu = Menu(
            id = 789,
            name = ""
        )
        
        assertEquals(789, menu.id)
        assertEquals("", menu.name)
    }

    @Test
    fun menuZeroId() {
        val menu = Menu(id = 0)
        
        assertEquals(0, menu.id)
        assertNull(menu.name)
    }

    @Test
    fun menuNegativeId() {
        val menu = Menu(id = -5)
        
        assertEquals(-5, menu.id)
        assertNull(menu.name)
    }

    @Test
    fun menuLargeId() {
        val menu = Menu(id = Int.MAX_VALUE)
        
        assertEquals(Int.MAX_VALUE, menu.id)
        assertNull(menu.name)
    }

    @Test
    fun menuMinId() {
        val menu = Menu(id = Int.MIN_VALUE)
        
        assertEquals(Int.MIN_VALUE, menu.id)
        assertNull(menu.name)
    }

    @Test
    fun menuLongName() {
        val longName = "Very long menu name ".repeat(100)
        val menu = Menu(
            id = 1,
            name = longName
        )
        
        assertEquals(1, menu.id)
        assertEquals(longName, menu.name)
    }

    @Test
    fun menuSpecialCharacters() {
        val specialName = "Menu with special chars !@#$%^&*()_+-=[]{}|;':\",./<>?"
        val menu = Menu(
            id = 2,
            name = specialName
        )
        
        assertEquals(2, menu.id)
        assertEquals(specialName, menu.name)
    }

    @Test
    fun menuUnicodeCharacters() {
        val unicodeName = "Menu with unicode characters and symbols"
        val menu = Menu(
            id = 3,
            name = unicodeName
        )
        
        assertEquals(3, menu.id)
        assertEquals(unicodeName, menu.name)
    }

    @Test
    fun menuWhitespaceOnlyName() {
        val whitespaceName = "   \t\n   "
        val menu = Menu(
            id = 4,
            name = whitespaceName
        )
        
        assertEquals(4, menu.id)
        assertEquals(whitespaceName, menu.name)
    }

    @Test
    fun menuJsonSerialization() {
        val menu = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        val json = GsonProvider.gson.toJson(menu)
        
        assertNotNull(json)
        assertTrue(json.contains("\"id\":123"))
        assertTrue(json.contains("\"name\":\"Test Menu\""))
    }

    @Test
    fun menuJsonSerializationWithNullName() {
        val menu = Menu(
            id = 456,
            name = null
        )
        
        val json = GsonProvider.gson.toJson(menu)
        
        assertNotNull(json)
        assertTrue(json.contains("\"id\":456"))
    }

    @Test
    fun menuJsonSerializationWithDefaultId() {
        val menu = Menu(name = "Test Menu")
        
        val json = GsonProvider.gson.toJson(menu)
        
        assertNotNull(json)
        assertTrue(json.contains("\"id\":-1"))
        assertTrue(json.contains("\"name\":\"Test Menu\""))
    }

    @Test
    fun menuJsonDeserialization() {
        val json = """
            {
                "id": 123,
                "name": "Test Menu"
            }
        """.trimIndent()
        
        val menu = GsonProvider.gson.fromJson(json, Menu::class.java)
        
        assertNotNull(menu)
        assertEquals(123, menu.id)
        assertEquals("Test Menu", menu.name)
    }

    @Test
    fun menuJsonDeserializationWithNullName() {
        val json = """
            {
                "id": 456,
                "name": null
            }
        """.trimIndent()
        
        val menu = GsonProvider.gson.fromJson(json, Menu::class.java)
        
        assertNotNull(menu)
        assertEquals(456, menu.id)
        assertNull(menu.name)
    }

    @Test
    fun menuJsonDeserializationWithMissingName() {
        val json = """
            {
                "id": 789
            }
        """.trimIndent()
        
        val menu = GsonProvider.gson.fromJson(json, Menu::class.java)
        
        assertNotNull(menu)
        assertEquals(789, menu.id)
        assertNull(menu.name)
    }

    @Test
    fun menuJsonDeserializationWithMissingId() {
        val json = """
            {
                "name": "Test Menu"
            }
        """.trimIndent()
        
        val menu = GsonProvider.gson.fromJson(json, Menu::class.java)
        
        assertNotNull(menu)
        assertEquals(-1, menu.id) // Should use default value
        assertEquals("Test Menu", menu.name)
    }

    @Test
    fun menuJsonDeserializationWithEmptyObject() {
        val json = "{}"
        
        val menu = GsonProvider.gson.fromJson(json, Menu::class.java)
        
        assertNotNull(menu)
        assertEquals(-1, menu.id) // Should use default value
        assertNull(menu.name) // Should use default value
    }

    @Test
    fun menuJsonRoundTrip() {
        val originalMenu = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        val json = GsonProvider.gson.toJson(originalMenu)
        val deserializedMenu = GsonProvider.gson.fromJson(json, Menu::class.java)
        
        assertEquals(originalMenu, deserializedMenu)
    }

    @Test
    fun menuJsonRoundTripWithNullName() {
        val originalMenu = Menu(
            id = 456,
            name = null
        )
        
        val json = GsonProvider.gson.toJson(originalMenu)
        val deserializedMenu = GsonProvider.gson.fromJson(json, Menu::class.java)
        
        assertEquals(originalMenu, deserializedMenu)
    }

    @Test
    fun menuJsonDeserializationWithExtraFields() {
        val json = """
            {
                "id": 123,
                "name": "Test Menu",
                "extra_field": "should be ignored",
                "another_field": 456
            }
        """.trimIndent()
        
        val menu = GsonProvider.gson.fromJson(json, Menu::class.java)
        
        assertNotNull(menu)
        assertEquals(123, menu.id)
        assertEquals("Test Menu", menu.name)
    }

    @Test
    fun menuJsonDeserializationWithInvalidJson() {
        val invalidJson = "{ invalid json }"
        assertThrows(JsonSyntaxException::class.java) {
            GsonProvider.gson.fromJson(invalidJson, Menu::class.java)
        }
    }

    @Test
    fun menuEquals() {
        val menu1 = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        val menu2 = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        val menu3 = Menu(
            id = 456,
            name = "Different Menu"
        )
        
        val menu4 = Menu(
            id = 123,
            name = "Different Name"
        )
        
        assertEquals(menu1, menu2)
        assertNotEquals(menu1, menu3)
        assertNotEquals(menu1, menu4)
        assertNotEquals(menu1, null)
        assertNotEquals(menu1, "not a Menu")
    }

    @Test
    fun menuEqualsWithNullNames() {
        val menu1 = Menu(id = 123, name = null)
        val menu2 = Menu(id = 123, name = null)
        val menu3 = Menu(id = 123, name = "Test")
        
        assertEquals(menu1, menu2)
        assertNotEquals(menu1, menu3)
    }

    @Test
    fun menuHashCode() {
        val menu1 = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        val menu2 = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        assertEquals(menu1.hashCode(), menu2.hashCode())
    }

    @Test
    fun menuHashCodeWithNullName() {
        val menu1 = Menu(id = 123, name = null)
        val menu2 = Menu(id = 123, name = null)
        
        assertEquals(menu1.hashCode(), menu2.hashCode())
    }

    @Test
    fun menuToString() {
        val menu = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        val toStringResult = menu.toString()
        
        assertNotNull(toStringResult)
        assertTrue(toStringResult.contains("Menu"))
        assertTrue(toStringResult.contains("123"))
        assertTrue(toStringResult.contains("Test Menu"))
    }

    @Test
    fun menuToStringWithNullName() {
        val menu = Menu(
            id = 456,
            name = null
        )
        
        val toStringResult = menu.toString()
        
        assertNotNull(toStringResult)
        assertTrue(toStringResult.contains("Menu"))
        assertTrue(toStringResult.contains("456"))
        assertTrue(toStringResult.contains("null"))
    }

    @Test
    fun menuCopy() {
        val originalMenu = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        val copiedMenu = originalMenu.copy()
        
        assertEquals(originalMenu, copiedMenu)
        assertNotSame(originalMenu, copiedMenu)
    }

    @Test
    fun menuCopyWithChanges() {
        val originalMenu = Menu(
            id = 123,
            name = "Original Menu"
        )
        
        val modifiedMenu = originalMenu.copy(
            id = 456,
            name = "Modified Menu"
        )
        
        assertEquals(456, modifiedMenu.id)
        assertEquals("Modified Menu", modifiedMenu.name)
        
        // Original should remain unchanged
        assertEquals(123, originalMenu.id)
        assertEquals("Original Menu", originalMenu.name)
    }

    @Test
    fun menuCopyWithPartialChanges() {
        val originalMenu = Menu(
            id = 123,
            name = "Original Menu"
        )
        
        val modifiedIdMenu = originalMenu.copy(id = 456)
        val modifiedNameMenu = originalMenu.copy(name = "Modified Menu")
        
        assertEquals(456, modifiedIdMenu.id)
        assertEquals("Original Menu", modifiedIdMenu.name)
        
        assertEquals(123, modifiedNameMenu.id)
        assertEquals("Modified Menu", modifiedNameMenu.name)
    }

    @Test
    fun menuComponentFunctions() {
        val menu = Menu(
            id = 123,
            name = "Test Menu"
        )
        
        val (id, name) = menu
        
        assertEquals(123, id)
        assertEquals("Test Menu", name)
    }

    @Test
    fun menuComponentFunctionsWithNullName() {
        val menu = Menu(
            id = 456,
            name = null
        )
        
        val (id, name) = menu
        
        assertEquals(456, id)
        assertNull(name)
    }

    @Test
    fun menuDefaultIdValue() {
        val menu1 = Menu(name = "Test")
        val menu2 = Menu()
        
        assertEquals(-1, menu1.id)
        assertEquals(-1, menu2.id)
    }

    @Test
    fun menuEdgeCaseIds() {
        val menu1 = Menu(id = 0, name = "Zero ID")
        val menu2 = Menu(id = -1, name = "Negative One ID")
        val menu3 = Menu(id = 1, name = "Positive One ID")
        
        assertEquals(0, menu1.id)
        assertEquals(-1, menu2.id)
        assertEquals(1, menu3.id)
    }

    @Test
    fun menuDescribeContents() {
        val menu = Menu(id = 123, name = "Test Menu")
        
        assertEquals(0, menu.describeContents())
    }

    @Test
    fun menuWriteToParcel() {
        val menu = Menu(id = 123, name = "Test Menu")
        val parcel = mock(Parcel::class.java)
        val flags = 0
        
        menu.writeToParcel(parcel, flags)
        
        verify(parcel).writeInt(123)
        verify(parcel).writeString("Test Menu")
    }

    @Test
    fun menuWriteToParcelWithNullName() {
        val menu = Menu(id = 456, name = null)
        val parcel = mock(Parcel::class.java)
        val flags = 0
        
        menu.writeToParcel(parcel, flags)
        
        verify(parcel).writeInt(456)
        verify(parcel).writeString(null)
    }

    @Test
    fun menuParcelableInterface() {
        val menu = Menu(id = 123, name = "Test Menu")
        
        // Test that Menu implements Parcelable
        assertTrue(menu is android.os.Parcelable)
    }
}
