package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.CardButtonType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ContentCardButtonTest {

    @Test
    fun shouldCreateContentCardButton() {
        val button = ContentCardButton(
            title = "Button Title",
            link = "http://example.com/button",
            autoReply = true,
            eventParams = mapOf("key" to "value"),
            style = CardButtonType.Primary
        )
        assertEquals("Button Title", button.title)
        assertEquals("http://example.com/button", button.link)
        assertEquals(true, button.autoReply)
        assertEquals(mapOf("key" to "value"), button.eventParams)
        assertEquals(CardButtonType.Primary, button.style)
    }

    @Test
    fun shouldCreateContentCardButtonWithNullFields() {
        val button = ContentCardButton(
            title = null,
            link = null,
            autoReply = false,
            eventParams = null,
            style = CardButtonType.None
        )
        assertEquals(null, button.title)
        assertEquals(null, button.link)
        assertEquals(false, button.autoReply)
        assertEquals(null, button.eventParams)
        assertEquals(CardButtonType.None, button.style)
    }
} 
