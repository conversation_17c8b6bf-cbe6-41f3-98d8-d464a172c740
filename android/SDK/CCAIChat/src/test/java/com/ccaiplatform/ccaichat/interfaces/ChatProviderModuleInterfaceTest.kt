package com.ccaiplatform.ccaichat.interfaces

import android.content.Context
import com.ccaiplatform.ccaichat.model.ChatOptions
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.service.ChatNetworkServiceInterface
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.verify
import org.mockito.kotlin.eq

class ChatProviderModuleInterfaceTest {

    @Test
    fun chatProvider() {
        val module = Mockito.mock(ChatProviderModuleInterface::class.java)
        val context = Mockito.mock(Context::class.java)
        val chatResponse = ChatResponse(
            id = 123,
            lang = "en",
            providerType = "twilio",
            providerChannelId = "CH123",
            region = "us1"
        )
        val options = ChatOptions()
        val networkService = Mockito.mock(ChatNetworkServiceInterface::class.java)
        val provider = Mockito.mock(ChatProviderInterface::class.java)

        Mockito.`when`(module.chatProvider(
            eq(context),
            eq(chatResponse),
            eq(options),
            eq(networkService)
        )).thenReturn(provider)

        val result = module.chatProvider(context, chatResponse, options, networkService)

        verify(module).chatProvider(context, chatResponse, options, networkService)
        assert(result == provider)
    }

    @Test
    fun chatProviderWithNullOptions() {
        val module = Mockito.mock(ChatProviderModuleInterface::class.java)
        val context = Mockito.mock(Context::class.java)
        val chatResponse = ChatResponse(
            id = 123,
            lang = "en",
            providerType = "twilio",
            providerChannelId = "CH123",
            region = "us1"
        )
        val networkService = Mockito.mock(ChatNetworkServiceInterface::class.java)
        val provider = Mockito.mock(ChatProviderInterface::class.java)

        Mockito.`when`(module.chatProvider(
            eq(context),
            eq(chatResponse),
            eq(null),
            eq(networkService)
        )).thenReturn(provider)

        val result = module.chatProvider(context, chatResponse, null, networkService)

        verify(module).chatProvider(context, chatResponse, null, networkService)
        assert(result == provider)
    }
}
