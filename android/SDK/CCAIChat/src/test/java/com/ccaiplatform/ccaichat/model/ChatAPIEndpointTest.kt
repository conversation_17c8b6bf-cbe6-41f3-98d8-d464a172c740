package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.ChatAPIEndpoint.CHAT
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ChatAPIEndpointTest {

    @Test
    fun chatConstant() {
        assertEquals("chats", CHAT)
    }

    @Test
    fun chatToken() {
        val chatId = 123
        val expectedPath = "chats/${chatId}/tokens"
        assertEquals(expectedPath, ChatAPIEndpoint.chatToken(chatId))
    }

    @Test
    fun chatId() {
        val id = 123
        val expectedPath = "chats/${id}"
        assertEquals(expectedPath, ChatAPIEndpoint.chatId(id))
    }

    @Test
    fun chatEscalation() {
        val id = 456
        val expectedPath = "chats/${id}/escalations"
        assertEquals(expectedPath, ChatAPIEndpoint.chatEscalation(id))
    }

    @Test
    fun taskVaMessage() {
        val id = 456
        val expectedPath = "chats/${id}/task_va_messages"
        assertEquals(expectedPath, ChatAPIEndpoint.taskVaMessage(id))
    }

    @Test
    fun escalate() {
        val chatId = 123
        val escalationId = 456
        val expectedPath = "$CHAT/$chatId/escalations/$escalationId"
        assertEquals(expectedPath, ChatAPIEndpoint.escalate(chatId, escalationId))
    }

    @Test
    fun chatEvent() {
        val chatId = 123
        val expectedPath = "$CHAT/$chatId/end_user_event"
        assertEquals(expectedPath, ChatAPIEndpoint.chatEvent(chatId))
    }

    @Test
    fun uploadPhotos() {
        val id = 789
        val expectedPath = "chats/${id}/photos"
        assertEquals(expectedPath, ChatAPIEndpoint.uploadPhotos(id))
    }

    @Test
    fun uploadPhotosPreSignedUrl() {
        val id = 789
        val expectedPath = "chats/${id}/photos/upload"
        assertEquals(expectedPath, ChatAPIEndpoint.uploadPhotosPreSignedUrl(id))
    }

    @Test
    fun taskVaMessageWithMessageId() {
        val id = 456
        val messageId = 789
        val expectedPath = "chats/${id}/task_va_messages/${messageId}"
        assertEquals(expectedPath, ChatAPIEndpoint.taskVaMessage(id, messageId))
    }

    @Test
    fun customFormDetails() {
        val formId = 123
        val expectedPath = "forms/${formId}/details"
        assertEquals(expectedPath, ChatAPIEndpoint.customFormDetails(formId))
    }

    @Test
    fun submitCustomForm() {
        val expectedPath = "forms/submit"
        assertEquals(expectedPath, ChatAPIEndpoint.submitCustomForm())
    }

    @Test
    fun generateChatTranscript() {
        val chatId = 123
        val expectedPath = "chats/${chatId}/generate_transcript_pdf"
        assertEquals(expectedPath, ChatAPIEndpoint.generateChatTranscript(chatId))
    }

    @Test
    fun downloadChatTranscript() {
        val chatTranscriptId = 456
        val expectedPath = "chat_transcript_pdfs/${chatTranscriptId}/download_transcript"
        assertEquals(expectedPath, ChatAPIEndpoint.downloadChatTranscript(chatTranscriptId))
    }
}
