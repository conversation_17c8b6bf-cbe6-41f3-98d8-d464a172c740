package com.ccaiplatform.ccaichat.model

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test

class ChatOptionsTest {

    @Test
    fun defaultConstructor() {
        val options = ChatOptions()
        assertEquals(100, options.messagesPaginationSize)
    }

    @Test
    fun customConstructor() {
        val customSize = 20
        val options = ChatOptions(messagesPaginationSize = customSize)
        assertEquals(customSize, options.messagesPaginationSize)
    }

    @Test
    fun equality() {
        val options1 = ChatOptions()
        val options2 = ChatOptions()
        val options3 = ChatOptions(messagesPaginationSize = 20)

        assertEquals(options1, options2)
        assertFalse(options1 == options3)
    }

    @Test
    fun copy() {
        val original = ChatOptions()
        val modified = original.copy(messagesPaginationSize = 30)

        assertEquals(100, original.messagesPaginationSize)
        assertEquals(30, modified.messagesPaginationSize)
    }
}
