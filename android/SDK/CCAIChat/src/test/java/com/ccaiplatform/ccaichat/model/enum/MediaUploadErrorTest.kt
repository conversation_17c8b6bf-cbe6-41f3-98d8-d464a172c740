package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class MediaUploadErrorTest {

    @Test
    fun fileTooLargeIsSingleton() {
        val error1 = MediaUploadError.FileTooLarge
        val error2 = MediaUploadError.FileTooLarge
        assertSame(error1, error2)
        assertTrue(error1 is MediaUploadError.FileTooLarge)
    }

    @Test
    fun accessDeniedIsSingleton() {
        val error1 = MediaUploadError.AccessDenied
        val error2 = MediaUploadError.AccessDenied
        assertSame(error1, error2)
        assertTrue(error1 is MediaUploadError.AccessDenied)
    }

    @Test
    fun uploadErrorWithDefaultCode() {
        val error = MediaUploadError.UploadError()
        assertEquals(0, error.code)
    }

    @Test
    fun uploadErrorWithCustomCode() {
        val error = MediaUploadError.UploadError(404)
        assertEquals(404, error.code)
    }

    @Test
    fun uploadErrorEquality() {
        val error1 = MediaUploadError.UploadError(1)
        val error2 = MediaUploadError.UploadError(2)
        assertNotEquals(error1, error2)
    }

    @Test
    fun differentTypesAreNotEqual() {
        assertNotEquals(MediaUploadError.FileTooLarge, MediaUploadError.AccessDenied)
        assertNotEquals(MediaUploadError.FileTooLarge, MediaUploadError.UploadError())
        assertNotEquals(MediaUploadError.AccessDenied, MediaUploadError.UploadError())
    }
}
