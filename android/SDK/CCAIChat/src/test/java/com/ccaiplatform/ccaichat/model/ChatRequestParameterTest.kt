package com.ccaiplatform.ccaichat.model

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ChatRequestParameterTest {

    @Test
    fun checkDeviceId() {
        assertEquals("device_id", ChatRequestParameter.DEVICE_ID)
    }

    @Test
    fun checkProviderType() {
        assertEquals("provider_type", ChatRequestParameter.PROVIDER_TYPE)
    }

    @Test
    fun checkStatus() {
        assertEquals("status", ChatRequestParameter.STATUS)
    }

    @Test
    fun checkPage() {
        assertEquals("page", ChatRequestParameter.PAGE)
    }

    @Test
    fun checkPerPage() {
        assertEquals("per_page", ChatRequestParameter.PER_PAGE)
    }

    @Test
    fun checkDeflectionChannel() {
        assertEquals("deflection_channel", ChatRequestParameter.DEFLECTION_CHANNEL)
    }

    @Test
    fun checkEvent() {
        assertEquals("event", ChatRequestParameter.EVENT)
    }

    @Test
    fun checkContentCardClicked() {
        assertEquals("content_card_clicked", ChatRequestParameter.CONTENT_CARD_CLICKED)
    }

    @Test
    fun checkContentCardButtonClicked() {
        assertEquals("content_card_button_clicked", ChatRequestParameter.CONTENT_CARD_BUTTON_CLICKED)
    }

    @Test
    fun checkTitle() {
        assertEquals("title", ChatRequestParameter.TITLE)
    }

    @Test
    fun checkButtonTitle() {
        assertEquals("button_title", ChatRequestParameter.BUTTON_TITLE)
    }
}
