package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaikit.models.customdata.CustomData
import com.ccaiplatform.ccaikit.models.customdata.CustomDataPayload
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class ChatRequestTest {

    @Test
    fun defaultConstructor() {
        val request = ChatRequest()
        assertEquals(0, request.menuId)
        assertEquals("en", request.languageCode)
        assertNull(request.ticketId)
        assertNull(request.greetingOverride)
        assertNull(request.customData)
    }

    @Test
    fun customConstructor() {
        val menuId = 123
        val languageCode = "en"
        val ticketId = "ticket123"
        val greetingOverride = "Hello"
        val customData = CustomData("agagadadaiiajadja", CustomDataPayload())

        val request = ChatRequest(
            menuId = menuId,
            languageCode = languageCode,
            ticketId = ticketId,
            greetingOverride = greetingOverride,
            customData = customData
        )

        assertEquals(menuId, request.menuId)
        assertEquals(languageCode, request.languageCode)
        assertEquals(ticketId, request.ticketId)
        assertEquals(greetingOverride, request.greetingOverride)
        assertEquals(customData, request.customData)
    }

    @Test
    fun partialCustomConstructor() {
        val menuId = 123
        val languageCode = "en"
        val request = ChatRequest(
            menuId = menuId,
            languageCode = languageCode
        )

        assertEquals(menuId, request.menuId)
        assertEquals(languageCode, request.languageCode)
        assertNull(request.ticketId)
        assertNull(request.greetingOverride)
        assertNull(request.customData)
    }

    @Test
    fun equality() {
        val request1 = ChatRequest()
        val request2 = ChatRequest()
        val request3 = ChatRequest(menuId = 123)
        val request4 = ChatRequest(ticketId = "ticket123")
        val request5 = ChatRequest(greetingOverride = "Hello")
        val request6 = ChatRequest(customData = CustomData("agagadadaiiajadja", CustomDataPayload()))

        assertEquals(request1, request2)
        assertFalse(request1 == request3)
        assertFalse(request1 == request4)
        assertFalse(request1 == request5)
        assertFalse(request1 == request6)
    }

    @Test
    fun copy() {
        val original = ChatRequest()
        val modified = original.copy(
            menuId = 123,
            languageCode = "en",
            ticketId = "ticket123",
            greetingOverride = "Hello",
            customData = CustomData("agagadadaiiajadja", CustomDataPayload())
        )

        assertEquals(0, original.menuId)
        assertEquals("en", original.languageCode)
        assertNull(original.ticketId)
        assertNull(original.greetingOverride)
        assertNull(original.customData)

        assertEquals(123, modified.menuId)
        assertEquals("en", modified.languageCode)
        assertEquals("ticket123", modified.ticketId)
        assertEquals("Hello", modified.greetingOverride)
        assertEquals(CustomData("agagadadaiiajadja", CustomDataPayload()), modified.customData)
    }
}
