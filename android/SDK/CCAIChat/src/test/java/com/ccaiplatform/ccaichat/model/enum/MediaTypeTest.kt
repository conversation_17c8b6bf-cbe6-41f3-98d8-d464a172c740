package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class MediaTypeTest {

    private val gson: Gson = GsonBuilder()
        .registerTypeAdapter(MediaType::class.java, MediaTypeAdapter())
        .create()

    @Test
    fun fromValueReturnsCorrectEnum() {
        assertEquals(MediaType.Photo, MediaType.fromValue("photo"))
        assertEquals(MediaType.Photo, MediaType.fromValue("PHOTO"))
        assertEquals(MediaType.Screenshot, MediaType.fromValue("screenshot"))
        assertEquals(MediaType.Screenshot, MediaType.fromValue("SCREENSHOT"))
        assertNull(MediaType.fromValue("unknown"))
        assertNull(MediaType.fromValue(""))
    }

    @Test
    fun serializeEnumToJson() {
        assertEquals("\"photo\"", gson.toJson(MediaType.Photo))
        assertEquals("\"screenshot\"", gson.toJson(MediaType.Screenshot))
    }

    @Test
    fun deserializeJsonToEnum() {
        assertEquals(MediaType.Photo, gson.fromJson("\"photo\"", MediaType::class.java))
        assertEquals(MediaType.Screenshot, gson.fromJson("\"screenshot\"", MediaType::class.java))
        assertEquals(MediaType.Photo, gson.fromJson("\"PHOTO\"", MediaType::class.java))
        assertEquals(MediaType.Screenshot, gson.fromJson("\"SCREENSHOT\"", MediaType::class.java))
        assertNull(gson.fromJson("\"unknown\"", MediaType::class.java))
        assertNull(gson.fromJson("\"\"", MediaType::class.java))
    }
}
