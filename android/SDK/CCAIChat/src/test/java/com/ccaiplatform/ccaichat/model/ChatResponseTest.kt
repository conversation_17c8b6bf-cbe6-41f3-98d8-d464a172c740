package com.ccaiplatform.ccaichat.model

import android.os.Parcel
import com.ccaiplatform.ccaichat.model.enum.ChatStatus
import com.ccaiplatform.ccaikit.models.response.communication.HumanAgent
import com.ccaiplatform.ccaikit.models.response.communication.VirtualAgent
import com.google.gson.FieldNamingPolicy
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify


class ChatResponseTest {
    private val gson: Gson = GsonBuilder().setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES).create()

    @Test
    fun statusDecoding() {
        val statusPairs = listOf(
            "queued" to ChatStatus.Queued,
            "assigned" to ChatStatus.Assigned,
            "dismissed" to ChatStatus.Dismissed,
            "finished" to ChatStatus.Finished,
            "canceled" to ChatStatus.Canceled,
            "failed" to ChatStatus.Failed,
            "va_assigned" to ChatStatus.VaAssigned,
            "va_dismissed" to ChatStatus.VaDismissed
        )

        statusPairs.forEach { (statusString, expectedStatus) ->
            val json = """
            {
                "id": 123,
                "status": "$statusString"
            }
            """.trimIndent()

            val response = gson.fromJson(json, ChatResponse::class.java)
            assertEquals(expectedStatus, response.status)
        }
    }

    @Test
    fun completeResponseDecoding() {
        val json = """
        {
            "id": 123,
            "lang": "en",
            "status": "assigned",
            "status_text": "Agent will help you",
            "provider_type": "twillio",
            "provider_channel_id": "CH123",
            "region": "us1",
            "agent": {
                "id": 1,
                "name": "Jason Huang",
                "first_name": "Jason",
                "last_name": "Huang",
                "avatar_url": "https://ilabs.ujetdemo.co/assets2/default-virtual-agent-avatar-2.png"
            }
        }
        """.trimIndent()
        val response = gson.fromJson(json, ChatResponse::class.java)

        // Basic properties
        assertEquals(123, response.id)
        assertEquals("en", response.lang)
        assertEquals(ChatStatus.Assigned, response.status)
        assertEquals("Agent will help you", response.statusText)
        assertEquals("twillio", response.providerType)
        assertEquals("CH123", response.providerChannelId)
        assertEquals("us1", response.region)

        // Agent properties
        assertEquals(1, response.agent?.id)
        assertEquals("Jason Huang", response.agent?.name)
    }

    @Test
    fun minimalResponseDecoding() {
        val json = """
        {
            "id": 123
        }
        """.trimIndent()

        val response = gson.fromJson(json, ChatResponse::class.java)

        assertEquals(123, response.id)
        assertNull(response.lang)
        assertNull(response.status)
        assertNull(response.statusText)
        assertNull(response.providerType)
        assertNull(response.providerChannelId)
        assertNull(response.region)
        assertNull(response.agent)
    }

    @Test
    fun currentHumanAgent() {
        val json = """
        {
            "id": 123,
            "status": "assigned",
            "agent": {
                "id": 1,
                "name": "Jason Huang"
            }
        }
        """.trimIndent()

        val response = gson.fromJson(json, ChatResponse::class.java)
        assertEquals(1, response.agent?.id)
        assertEquals("Jason Huang", response.agent?.name)
    }

    @Test
    fun unknownStatusDecoding() {
        val json = """
        {
            "id": 123,
            "status": "unknown_status"
        }
        """.trimIndent()

        val response = gson.fromJson(json, ChatResponse::class.java)
        assertEquals(ChatStatus.Unknown("unknown_status"), response.status)
    }

    @Test
    fun invalidJSONDecoding() {
        val invalidJson = "Invalid JSON"
        assertThrows(Exception::class.java) {
            gson.fromJson(invalidJson, ChatResponse::class.java)
        }
    }

    @Test
    fun chatStatusRawValue() {
        assertEquals("queued", ChatStatus.Queued.rawValue)
        assertEquals("assigned", ChatStatus.Assigned.rawValue)
        assertEquals("va_assigned", ChatStatus.VaAssigned.rawValue)
        assertEquals("va_dismissed", ChatStatus.VaDismissed.rawValue)
    }

    @Test
    fun chatStatusMapping() {
        assertEquals(ChatStatus.VaAssigned, ChatStatus.fromRawValue("va_assigned"))
        assertEquals(ChatStatus.Queued, ChatStatus.fromRawValue("queued"))
    }

    @Test
    fun chatStatusInProgressList() {
        val inProgressStatuses = ChatStatus.inProgressStatus
        assertTrue(inProgressStatuses.contains(ChatStatus.Queued))
        assertTrue(inProgressStatuses.contains(ChatStatus.Assigned))
        assertTrue(inProgressStatuses.contains(ChatStatus.VaAssigned))
        assertTrue(inProgressStatuses.contains(ChatStatus.Dismissed))
        assertTrue(inProgressStatuses.contains(ChatStatus.VaDismissed))
        assertTrue(!inProgressStatuses.contains(ChatStatus.Finished))
        assertTrue(!inProgressStatuses.contains(ChatStatus.Canceled))
        assertTrue(!inProgressStatuses.contains(ChatStatus.Failed))
    }

    @Test
    fun currentAgentAssignedStatus() {
        val humanAgent = HumanAgent(id = 1, name = "Test Agent")
        val response = ChatResponse(
            id = 123,
            status = ChatStatus.Assigned,
            agent = humanAgent
        )
        
        assertEquals(humanAgent, response.currentAgent)
    }

    @Test
    fun currentAgentDismissedStatus() {
        val humanAgent = HumanAgent(id = 1, name = "Test Agent")
        val response = ChatResponse(
            id = 123,
            status = ChatStatus.Dismissed,
            agent = humanAgent
        )
        
        assertEquals(humanAgent, response.currentAgent)
    }

    @Test
    fun currentAgentVaAssignedStatus() {
        val virtualAgent = VirtualAgent(id = 1, name = "Test Virtual Agent")
        val response = ChatResponse(
            id = 123,
            status = ChatStatus.VaAssigned,
            virtualAgent = virtualAgent
        )
        
        assertEquals(virtualAgent, response.currentAgent)
    }

    @Test
    fun currentAgentVaDismissedStatus() {
        val virtualAgent = VirtualAgent(id = 1, name = "Test Virtual Agent")
        val response = ChatResponse(
            id = 123,
            status = ChatStatus.VaDismissed,
            virtualAgent = virtualAgent
        )
        
        assertEquals(virtualAgent, response.currentAgent)
    }

    @Test
    fun currentAgentQueuedStatus() {
        val humanAgent = HumanAgent(id = 1, name = "Test Agent")
        val virtualAgent = VirtualAgent(id = 2, name = "Test Virtual Agent")
        val response = ChatResponse(
            id = 123,
            status = ChatStatus.Queued,
            agent = humanAgent,
            virtualAgent = virtualAgent
        )
        
        assertNull(response.currentAgent)
    }

    @Test
    fun currentAgentFinishedStatus() {
        val humanAgent = HumanAgent(id = 1, name = "Test Agent")
        val response = ChatResponse(
            id = 123,
            status = ChatStatus.Finished,
            agent = humanAgent
        )
        
        assertNull(response.currentAgent)
    }

    @Test
    fun currentAgentNullStatus() {
        val humanAgent = HumanAgent(id = 1, name = "Test Agent")
        val response = ChatResponse(
            id = 123,
            status = null,
            agent = humanAgent
        )
        
        assertNull(response.currentAgent)
    }

    @Test
    fun getAgentBySendIdFoundInAllAgents() {
        val agent1 = HumanAgent(id = 1, name = "Agent 1")
        val agent2 = HumanAgent(id = 2, name = "Agent 2")
        val response = ChatResponse(
            id = 123,
            allAgents = listOf(agent1, agent2)
        )
        
        assertEquals(agent1, response.getAgentBySendId("user-1"))
        assertEquals(agent2, response.getAgentBySendId("user-2"))
    }

    @Test
    fun getAgentBySendIdFoundInAllVirtualAgents() {
        val virtualAgent1 = VirtualAgent(id = 1, name = "Virtual Agent 1")
        val virtualAgent2 = VirtualAgent(id = 2, name = "Virtual Agent 2")
        val response = ChatResponse(
            id = 123,
            allVirtualAgents = listOf(virtualAgent1, virtualAgent2)
        )
        
        assertEquals(virtualAgent1, response.getAgentBySendId("virtual_agent-1"))
        assertEquals(virtualAgent2, response.getAgentBySendId("virtual_agent-2"))
    }

    @Test
    fun getAgentBySendIdPreferHumanAgent() {
        val humanAgent = HumanAgent(id = 1, name = "Human Agent")
        val virtualAgent = VirtualAgent(id = 1, name = "Virtual Agent")
        val response = ChatResponse(
            id = 123,
            allAgents = listOf(humanAgent),
            allVirtualAgents = listOf(virtualAgent)
        )
        
        // Should return human agent when looking for human agent ID
        assertEquals(humanAgent, response.getAgentBySendId("user-1"))
        assertEquals(virtualAgent, response.getAgentBySendId("virtual_agent-1"))
    }

    @Test
    fun getAgentBySendIdNotFound() {
        val agent = HumanAgent(id = 1, name = "Agent 1")
        val response = ChatResponse(
            id = 123,
            allAgents = listOf(agent)
        )
        
        assertNull(response.getAgentBySendId("999"))
    }

    @Test
    fun getAgentBySendIdNullSendId() {
        val agent = HumanAgent(id = 1, name = "Agent 1")
        val response = ChatResponse(
            id = 123,
            allAgents = listOf(agent)
        )
        
        assertNull(response.getAgentBySendId(null))
    }

    @Test
    fun getAgentBySendIdEmptyLists() {
        val response = ChatResponse(
            id = 123,
            allAgents = emptyList(),
            allVirtualAgents = emptyList()
        )
        
        assertNull(response.getAgentBySendId("user-1"))
    }

    @Test
    fun getAgentBySendIdNullLists() {
        val response = ChatResponse(
            id = 123,
            allAgents = null,
            allVirtualAgents = null
        )
        
        assertNull(response.getAgentBySendId("user-1"))
    }

    @Test
    fun getAgentBySendIdMixedLists() {
        val humanAgent = HumanAgent(id = 1, name = "Human Agent")
        val virtualAgent = VirtualAgent(id = 2, name = "Virtual Agent")
        val response = ChatResponse(
            id = 123,
            allAgents = listOf(humanAgent),
            allVirtualAgents = listOf(virtualAgent)
        )
        
        assertEquals(humanAgent, response.getAgentBySendId("user-1"))
        assertEquals(virtualAgent, response.getAgentBySendId("virtual_agent-2"))
        assertNull(response.getAgentBySendId("user-3"))
    }

    @Test
    fun describeContents() {
        val response = ChatResponse(id = 123)
        assertEquals(0, response.describeContents())
    }

    @Test
    fun writeToParcel() {
        val humanAgent = HumanAgent(id = 1, name = "Test Agent")
        val virtualAgent = VirtualAgent(id = 2, name = "Test Virtual Agent")
        val menu = Menu(id = 1, name = "Test Menu")
        
        val response = ChatResponse(
            id = 123,
            lang = "en",
            status = ChatStatus.Assigned,
            statusText = "Agent assigned",
            providerType = "twillio",
            providerChannelId = "CH123",
            region = "us1",
            timeoutAt = "2024-01-01T10:00:00Z",
            agent = humanAgent,
            virtualAgent = virtualAgent,
            allAgents = listOf(humanAgent),
            allVirtualAgents = listOf(virtualAgent),
            menus = listOf(menu)
        )
        
        val parcel = mock(Parcel::class.java)
        response.writeToParcel(parcel, 0)
        
        // Verify that writeToParcel was called (implementation details handled by @Parcelize)
        verify(parcel).writeInt(123)
    }

    @Test
    fun writeToParcelWithNullValues() {
        val response = ChatResponse(id = 456)
        val parcel = mock(Parcel::class.java)
        
        response.writeToParcel(parcel, 0)
        
        verify(parcel).writeInt(456)
    }

    @Test
    fun getTimeoutAt() {
        val timeoutValue = "2024-01-01T10:00:00Z"
        val response = ChatResponse(id = 123, timeoutAt = timeoutValue)
        
        assertEquals(timeoutValue, response.timeoutAt)
    }

    @Test
    fun getTimeoutAtNull() {
        val response = ChatResponse(id = 123, timeoutAt = null)
        
        assertNull(response.timeoutAt)
    }

    @Test
    fun getVirtualAgent() {
        val virtualAgent = VirtualAgent(id = 1, name = "Test Virtual Agent")
        val response = ChatResponse(id = 123, virtualAgent = virtualAgent)
        
        assertEquals(virtualAgent, response.virtualAgent)
    }

    @Test
    fun getVirtualAgentNull() {
        val response = ChatResponse(id = 123, virtualAgent = null)
        
        assertNull(response.virtualAgent)
    }

    @Test
    fun getAllAgents() {
        val agent1 = HumanAgent(id = 1, name = "Agent 1")
        val agent2 = HumanAgent(id = 2, name = "Agent 2")
        val agentList = listOf(agent1, agent2)
        val response = ChatResponse(id = 123, allAgents = agentList)
        
        assertEquals(agentList, response.allAgents)
        assertEquals(2, response.allAgents?.size)
    }

    @Test
    fun getAllAgentsNull() {
        val response = ChatResponse(id = 123, allAgents = null)
        
        assertNull(response.allAgents)
    }

    @Test
    fun getAllAgentsEmpty() {
        val response = ChatResponse(id = 123, allAgents = emptyList())
        
        assertEquals(emptyList<HumanAgent>(), response.allAgents)
        assertEquals(0, response.allAgents?.size)
    }

    @Test
    fun getAllVirtualAgents() {
        val virtualAgent1 = VirtualAgent(id = 1, name = "Virtual Agent 1")
        val virtualAgent2 = VirtualAgent(id = 2, name = "Virtual Agent 2")
        val virtualAgentList = listOf(virtualAgent1, virtualAgent2)
        val response = ChatResponse(id = 123, allVirtualAgents = virtualAgentList)
        
        assertEquals(virtualAgentList, response.allVirtualAgents)
        assertEquals(2, response.allVirtualAgents?.size)
    }

    @Test
    fun getAllVirtualAgentsNull() {
        val response = ChatResponse(id = 123, allVirtualAgents = null)
        
        assertNull(response.allVirtualAgents)
    }

    @Test
    fun getAllVirtualAgentsEmpty() {
        val response = ChatResponse(id = 123, allVirtualAgents = emptyList())
        
        assertEquals(emptyList<VirtualAgent>(), response.allVirtualAgents)
        assertEquals(0, response.allVirtualAgents?.size)
    }

    @Test
    fun getMenus() {
        val menu1 = Menu(id = 1, name = "Menu 1")
        val menu2 = Menu(id = 2, name = "Menu 2")
        val menuList = listOf(menu1, menu2)
        val response = ChatResponse(id = 123, menus = menuList)
        
        assertEquals(menuList, response.menus)
        assertEquals(2, response.menus?.size)
    }

    @Test
    fun getMenusNull() {
        val response = ChatResponse(id = 123, menus = null)
        
        assertNull(response.menus)
    }

    @Test
    fun getMenusEmpty() {
        val response = ChatResponse(id = 123, menus = emptyList())
        
        assertEquals(emptyList<Menu>(), response.menus)
        assertEquals(0, response.menus?.size)
    }

    @Test
    fun parcelableInterface() {
        val humanAgent = HumanAgent(id = 1, name = "Test Agent")
        val virtualAgent = VirtualAgent(id = 2, name = "Test Virtual Agent")
        val menu = Menu(id = 1, name = "Test Menu")
        
        val response = ChatResponse(
            id = 123,
            lang = "en",
            status = ChatStatus.Assigned,
            statusText = "Agent assigned",
            providerType = "twillio",
            providerChannelId = "CH123",
            region = "us1",
            timeoutAt = "2024-01-01T10:00:00Z",
            agent = humanAgent,
            virtualAgent = virtualAgent,
            allAgents = listOf(humanAgent),
            allVirtualAgents = listOf(virtualAgent),
            menus = listOf(menu)
        )
        
        // Test that ChatResponse implements Parcelable
        assertTrue(response is android.os.Parcelable)
        assertEquals(0, response.describeContents())
    }

    @Test
    fun allGettersWithData() {
        val humanAgent = HumanAgent(id = 1, name = "Test Agent")
        val virtualAgent = VirtualAgent(id = 2, name = "Test Virtual Agent")
        val menu = Menu(id = 1, name = "Test Menu")
        
        val response = ChatResponse(
            id = 123,
            lang = "en",
            status = ChatStatus.Assigned,
            statusText = "Agent assigned",
            providerType = "twillio",
            providerChannelId = "CH123",
            region = "us1",
            timeoutAt = "2024-01-01T10:00:00Z",
            agent = humanAgent,
            virtualAgent = virtualAgent,
            allAgents = listOf(humanAgent),
            allVirtualAgents = listOf(virtualAgent),
            menus = listOf(menu)
        )
        
        // Test all getters
        assertEquals(123, response.id)
        assertEquals("en", response.lang)
        assertEquals(ChatStatus.Assigned, response.status)
        assertEquals("Agent assigned", response.statusText)
        assertEquals("twillio", response.providerType)
        assertEquals("CH123", response.providerChannelId)
        assertEquals("us1", response.region)
        assertEquals("2024-01-01T10:00:00Z", response.timeoutAt)
        assertEquals(humanAgent, response.agent)
        assertEquals(virtualAgent, response.virtualAgent)
        assertEquals(listOf(humanAgent), response.allAgents)
        assertEquals(listOf(virtualAgent), response.allVirtualAgents)
        assertEquals(listOf(menu), response.menus)
    }
}
