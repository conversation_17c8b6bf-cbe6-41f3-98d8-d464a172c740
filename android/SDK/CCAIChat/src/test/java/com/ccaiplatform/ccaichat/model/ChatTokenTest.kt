package com.ccaiplatform.ccaichat.model

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ChatTokenTest {

    @Test
    fun constructor() {
        val tokenValue = "test_token"
        val chatToken = ChatToken(tokenValue)
        assertEquals(tokenValue, chatToken.token)
    }

    @Test
    fun equality() {
        val token1 = ChatToken("token1")
        val token2 = ChatToken("token1")
        val token3 = ChatToken("token2")

        assertEquals(token1, token2)
        assertFalse(token1 == token3)
    }

    @Test
    fun copy() {
        val original = ChatToken("original_token")
        val modified = original.copy(token = "new_token")

        assertEquals("original_token", original.token)
        assertEquals("new_token", modified.token)
    }

    @Test
    fun chatTokenSerializationDeserialization() {
        val tokenValue = "test_token"
        val chatToken = ChatToken(tokenValue)
        val gson: Gson = GsonBuilder().create()
        val json = gson.toJson(chatToken)
        val deserialized = gson.fromJson(json, ChatToken::class.java)
        assertEquals(chatToken, deserialized)
        // Check field name in JSON
        assertTrue(json.contains("\"token\""))
    }

    @Test
    fun chatTokenToString() {
        val chatToken = ChatToken("test_token")
        assert(chatToken.toString().isNotEmpty())
    }
}
