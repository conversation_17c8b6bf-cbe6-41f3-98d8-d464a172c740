package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.ccaiplatform.ccaikit.models.response.communication.Agent
import com.ccaiplatform.ccaikit.models.response.communication.VirtualAgent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.Date

class ChatHistoryInfoTest {
    private lateinit var chatMessages: List<ChatMessage>
    private lateinit var agents: List<Agent>
    private var nextPage: Int? = null

    @BeforeEach
    fun setup() {
        val messageBody1 = ChatMessageBody(
            type = ChatMessageBodyType.Text,
            content = "Hello"
        )
        val messageBody2 = ChatMessageBody(
            type = ChatMessageBodyType.Text,
            content = "Hi"
        )
        
        chatMessages = listOf(
            ChatMessage(
                index = 1,
                date = Date(),
                author = "end_user-1",
                bodyRaw = messageBody1.toJSONString()
            ),
            ChatMessage(
                index = 2,
                date = Date(),
                author = "virtual_agent-1",
                bodyRaw = messageBody2.toJSONString()
            )
        )
        
        agents = listOf(
            VirtualAgent(
                id = 1,
                name = "Agent1",
                avatarUrl = "avatar1"
            ),
            VirtualAgent(
                id = 2,
                name = "Agent2",
                avatarUrl = "avatar2"
            )
        )
        nextPage = 2
    }

    @Test
    fun createChatHistoryInfoWithNextPage() {
        val chatHistoryInfo = ChatHistoryInfo(chatMessages, agents, nextPage)

        assertEquals(chatMessages, chatHistoryInfo.chatMessages)
        assertEquals(agents, chatHistoryInfo.agents)
        assertEquals(nextPage, chatHistoryInfo.nextPage)
    }

    @Test
    fun createChatHistoryInfoWithoutNextPage() {
        val chatHistoryInfo = ChatHistoryInfo(chatMessages, agents, null)

        assertEquals(chatMessages, chatHistoryInfo.chatMessages)
        assertEquals(agents, chatHistoryInfo.agents)
        assertNull(chatHistoryInfo.nextPage)
    }

    @Test
    fun createChatHistoryInfoWithEmptyLists() {
        val emptyMessages = emptyList<ChatMessage>()
        val emptyAgents = emptyList<Agent>()
        val chatHistoryInfo = ChatHistoryInfo(emptyMessages, emptyAgents, nextPage)

        assertEquals(emptyMessages, chatHistoryInfo.chatMessages)
        assertEquals(emptyAgents, chatHistoryInfo.agents)
        assertEquals(nextPage, chatHistoryInfo.nextPage)
    }
}
