package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ChatProviderErrorTest {

    @Test
    fun invalidToken() {
        val error = ChatProviderError.InvalidToken
        assertTrue(error is ChatProviderError.InvalidToken)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun failedToInitialize() {
        val error = ChatProviderError.FailedToInitialize
        assertTrue(error is ChatProviderError.FailedToInitialize)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun failedToGetChat() {
        val error = ChatProviderError.FailedToGetChat
        assertTrue(error is ChatProviderError.FailedToGetChat)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun failedToGetChatId() {
        val error = ChatProviderError.FailedToGetChatId
        assertTrue(error is ChatProviderError.FailedToGetChatId)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun failedToGetIdentity() {
        val error = ChatProviderError.FailedToGetIdentity
        assertTrue(error is ChatProviderError.FailedToGetIdentity)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun failedToJoin() {
        val error = ChatProviderError.FailedToJoin
        assertTrue(error is ChatProviderError.FailedToJoin)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun failedToGetChatMessages() {
        val error = ChatProviderError.FailedToGetChatMessages
        assertTrue(error is ChatProviderError.FailedToGetChatMessages)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun invalidConversation() {
        val error = ChatProviderError.InvalidConversation
        assertTrue(error is ChatProviderError.InvalidConversation)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun failedToSendMessage() {
        val error = ChatProviderError.FailedToSendMessage
        assertTrue(error is ChatProviderError.FailedToSendMessage)
        assertTrue(error is ChatProviderError)
        assertTrue(error is Exception)
    }

    @Test
    fun equality() {
        // Test object equality
        assertEquals(ChatProviderError.InvalidToken, ChatProviderError.InvalidToken)
        assertEquals(ChatProviderError.FailedToInitialize, ChatProviderError.FailedToInitialize)
        assertEquals(ChatProviderError.FailedToGetChat, ChatProviderError.FailedToGetChat)
        assertEquals(ChatProviderError.FailedToGetChatId, ChatProviderError.FailedToGetChatId)
        assertEquals(ChatProviderError.FailedToGetIdentity, ChatProviderError.FailedToGetIdentity)
        assertEquals(ChatProviderError.FailedToJoin, ChatProviderError.FailedToJoin)
        assertEquals(ChatProviderError.FailedToGetChatMessages, ChatProviderError.FailedToGetChatMessages)
        assertEquals(ChatProviderError.InvalidConversation, ChatProviderError.InvalidConversation)
        assertEquals(ChatProviderError.FailedToSendMessage, ChatProviderError.FailedToSendMessage)
    }
}
