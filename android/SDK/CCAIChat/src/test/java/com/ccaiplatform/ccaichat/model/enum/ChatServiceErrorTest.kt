package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ChatServiceErrorTest {

    @Test
    fun failedToGetProvider() {
        val error = ChatServiceError.FailedToGetProvider
        assertTrue(error is ChatServiceError.FailedToGetProvider)
        assertTrue(error is ChatServiceError)
        assertTrue(error is Error)
    }

    @Test
    fun failedToGetChat() {
        val error = ChatServiceError.FailedToGetChat
        assertTrue(error is ChatServiceError.FailedToGetChat)
        assertTrue(error is ChatServiceError)
        assertTrue(error is Error)
    }

    @Test
    fun equality() {
        // Test object equality
        assertEquals(ChatServiceError.FailedToGetProvider, ChatServiceError.FailedToGetProvider)
        assertEquals(ChatServiceError.FailedToGetChat, ChatServiceError.FailedToGetChat)
    }
}
