package com.ccaiplatform.ccaichat.model.enum

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ChatProviderStateTest {

    @Test
    fun noneState() {
        val state = ChatProviderState.None
        when (state) {
            is ChatProviderState.None -> assertTrue(true)
            else -> assertTrue(false)
        }
    }

    @Test
    fun connectingState() {
        val state = ChatProviderState.Connecting
        when (state) {
            is ChatProviderState.Connecting -> assertTrue(true)
            else -> assertTrue(false)
        }
    }

    @Test
    fun connectedState() {
        val state = ChatProviderState.Connected
        when (state) {
            is ChatProviderState.Connected -> assertTrue(true)
            else -> assertTrue(false)
        }
    }

    @Test
    fun disconnectedState() {
        val state = ChatProviderState.Disconnected
        when (state) {
            is ChatProviderState.Disconnected -> assertTrue(true)
            else -> assertTrue(false)
        }
    }

    @Test
    fun deletedState() {
        val state = ChatProviderState.Deleted
        when (state) {
            is ChatProviderState.Deleted -> assertTrue(true)
            else -> assertTrue(false)
        }
    }

    @Test
    fun errorState() {
        val error = ChatProviderError.FailedToGetChat
        val description = "Test error description"
        val state = ChatProviderState.Error(error, description)

        when (state) {
            is ChatProviderState.Error -> {
                assertEquals(error, state.error)
                assertEquals(description, state.description)
            }
            else -> assertTrue(false)
        }
    }

    @Test
    fun errorStateWithNullDescription() {
        val error = ChatProviderError.FailedToGetChat
        val state = ChatProviderState.Error(error, null)

        when (state) {
            is ChatProviderState.Error -> {
                assertEquals(error, state.error)
                assertNull(state.description)
            }
            else -> assertTrue(false)
        }
    }

    @Test
    fun equality() {
        // Test object equality
        assertEquals(ChatProviderState.None, ChatProviderState.None)
        assertEquals(ChatProviderState.Connecting, ChatProviderState.Connecting)
        assertEquals(ChatProviderState.Connected, ChatProviderState.Connected)
        assertEquals(ChatProviderState.Disconnected, ChatProviderState.Disconnected)
        assertEquals(ChatProviderState.Deleted, ChatProviderState.Deleted)

        // Test Error state equality
        val error1 = ChatProviderState.Error(ChatProviderError.FailedToGetChat, "test")
        val error2 = ChatProviderState.Error(ChatProviderError.FailedToGetChat, "test")
        val error3 = ChatProviderState.Error(ChatProviderError.FailedToGetChat, "different")

        assertEquals(error1, error2)
        assertFalse(error1 == error3)
    }
}
