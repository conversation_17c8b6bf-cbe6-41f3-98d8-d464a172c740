package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ChatMessageBodyTypeTest {
    private lateinit var gson: Gson

    @BeforeEach
    fun setUp() {
        gson = GsonBuilder()
            .registerTypeAdapter(ChatMessageBodyType::class.java, ChatMessageBodyTypeAdapter())
            .create()
    }

    @Test
    fun enumValue() {
        // Test all enum values
        assertEquals("unknown", ChatMessageBodyType.Unknown.value)
        assertEquals("text", ChatMessageBodyType.Text.value)
        assertEquals("photo", ChatMessageBodyType.Photo.value)
        assertEquals("video", ChatMessageBodyType.Video.value)
        assertEquals("noti", ChatMessageBodyType.Notification.value)
        assertEquals("text_template", ChatMessageBodyType.TextTemplate.value)
        assertEquals("markdown", ChatMessageBodyType.Markdown.value)
        assertEquals("markdown_template", ChatMessageBodyType.MarkdownTemplate.value)
        assertEquals("inline_button", ChatMessageBodyType.InlineButton.value)
        assertEquals("sticky_button", ChatMessageBodyType.StickyButton.value)
        assertEquals("document", ChatMessageBodyType.Document.value)
        assertEquals("image", ChatMessageBodyType.Image.value)
        assertEquals("content_card", ChatMessageBodyType.ContentCard.value)
        assertEquals("form", ChatMessageBodyType.Form.value)
        assertEquals("form_complete", ChatMessageBodyType.FormComplete.value)
        assertEquals("server_message", ChatMessageBodyType.ServerMessage.value)
    }

    @Test
    fun fromValue() {
        // Test valid values
        assertEquals(ChatMessageBodyType.Text, ChatMessageBodyType.fromValue("text"))
        assertEquals(ChatMessageBodyType.Photo, ChatMessageBodyType.fromValue("photo"))
        assertEquals(ChatMessageBodyType.Video, ChatMessageBodyType.fromValue("video"))
        assertEquals(ChatMessageBodyType.Notification, ChatMessageBodyType.fromValue("noti"))
        assertEquals(ChatMessageBodyType.TextTemplate, ChatMessageBodyType.fromValue("text_template"))
        assertEquals(ChatMessageBodyType.Markdown, ChatMessageBodyType.fromValue("markdown"))
        assertEquals(ChatMessageBodyType.MarkdownTemplate, ChatMessageBodyType.fromValue("markdown_template"))
        assertEquals(ChatMessageBodyType.InlineButton, ChatMessageBodyType.fromValue("inline_button"))
        assertEquals(ChatMessageBodyType.StickyButton, ChatMessageBodyType.fromValue("sticky_button"))
        assertEquals(ChatMessageBodyType.Document, ChatMessageBodyType.fromValue("document"))
        assertEquals(ChatMessageBodyType.Image, ChatMessageBodyType.fromValue("image"))
        assertEquals(ChatMessageBodyType.ContentCard, ChatMessageBodyType.fromValue("content_card"))
        assertEquals(ChatMessageBodyType.Form, ChatMessageBodyType.fromValue("form"))
        assertEquals(ChatMessageBodyType.FormComplete, ChatMessageBodyType.fromValue("form_complete"))
        assertEquals(ChatMessageBodyType.ServerMessage, ChatMessageBodyType.fromValue("server_message"))

        // Test invalid value
        assertEquals(ChatMessageBodyType.Unknown, ChatMessageBodyType.fromValue("invalid"))
        assertEquals(ChatMessageBodyType.Unknown, ChatMessageBodyType.fromValue(""))
    }

    @Test
    fun serialization() {
        // Test serialization of each type
        assertEquals("\"text\"", gson.toJson(ChatMessageBodyType.Text))
        assertEquals("\"photo\"", gson.toJson(ChatMessageBodyType.Photo))
        assertEquals("\"video\"", gson.toJson(ChatMessageBodyType.Video))
        assertEquals("\"noti\"", gson.toJson(ChatMessageBodyType.Notification))
        assertEquals("\"text_template\"", gson.toJson(ChatMessageBodyType.TextTemplate))
        assertEquals("\"markdown\"", gson.toJson(ChatMessageBodyType.Markdown))
        assertEquals("\"markdown_template\"", gson.toJson(ChatMessageBodyType.MarkdownTemplate))
        assertEquals("\"inline_button\"", gson.toJson(ChatMessageBodyType.InlineButton))
        assertEquals("\"sticky_button\"", gson.toJson(ChatMessageBodyType.StickyButton))
        assertEquals("\"document\"", gson.toJson(ChatMessageBodyType.Document))
        assertEquals("\"image\"", gson.toJson(ChatMessageBodyType.Image))
        assertEquals("\"content_card\"", gson.toJson(ChatMessageBodyType.ContentCard))
        assertEquals("\"form\"", gson.toJson(ChatMessageBodyType.Form))
        assertEquals("\"form_complete\"", gson.toJson(ChatMessageBodyType.FormComplete))
        assertEquals("\"server_message\"", gson.toJson(ChatMessageBodyType.ServerMessage))
    }

    @Test
    fun deserialization() {
        // Test deserialization of each type
        assertEquals(ChatMessageBodyType.Text, gson.fromJson("\"text\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.Photo, gson.fromJson("\"photo\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.Video, gson.fromJson("\"video\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.Notification, gson.fromJson("\"noti\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.TextTemplate, gson.fromJson("\"text_template\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.Markdown, gson.fromJson("\"markdown\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.MarkdownTemplate, gson.fromJson("\"markdown_template\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.InlineButton, gson.fromJson("\"inline_button\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.StickyButton, gson.fromJson("\"sticky_button\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.Document, gson.fromJson("\"document\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.Image, gson.fromJson("\"image\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.ContentCard, gson.fromJson("\"content_card\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.Form, gson.fromJson("\"form\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.FormComplete, gson.fromJson("\"form_complete\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.ServerMessage, gson.fromJson("\"server_message\"", ChatMessageBodyType::class.java))

        // Test invalid values
        assertEquals(ChatMessageBodyType.Unknown, gson.fromJson("\"invalid\"", ChatMessageBodyType::class.java))
        assertEquals(ChatMessageBodyType.Unknown, gson.fromJson("\"\"", ChatMessageBodyType::class.java))
    }

    @Test
    fun serializationDeserializationCycle() {
        // Test full cycle for each type
        val types = ChatMessageBodyType.entries.toTypedArray()
        for (type in types) {
            val json = gson.toJson(type)
            val deserialized = gson.fromJson(json, ChatMessageBodyType::class.java)
            assertEquals(type, deserialized)
        }
    }

    @Test
    fun encodeDecode() {
        val json = gson.toJson(ChatMessageBodyType.Photo)
        val photo = gson.fromJson(json, ChatMessageBodyType::class.java)
        assertEquals(ChatMessageBodyType.Photo, photo)
    }
}
