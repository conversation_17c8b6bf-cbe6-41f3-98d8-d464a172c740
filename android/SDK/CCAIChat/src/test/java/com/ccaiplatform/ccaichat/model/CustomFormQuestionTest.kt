package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.CustomFormAnswer
import com.ccaiplatform.ccaichat.model.enum.CustomFormQuestionType
import com.ccaiplatform.ccaikit.util.GsonProvider
import com.google.gson.JsonSyntaxException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNotSame
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class CustomFormQuestionTest {

    @Test
    fun customFormQuestionDefaultConstructor() {
        val question = CustomFormQuestion()
        
        assertNull(question.id)
        assertEquals(CustomFormQuestionType.None, question.type)
        assertNull(question.isMandatory)
        assertNull(question.isMasked)
        assertNull(question.position)
        assertNull(question.question)
        assertNull(question.isMultiselect)
        assertNull(question.placeholder)
        assertNull(question.contentType)
        assertNull(question.characterLimit)
        assertNull(question.helpText)
        assertNull(question.toggleOnText)
        assertNull(question.toggleOffText)
        assertNull(question.options)
        assertNull(question.answer)
    }

    @Test
    fun customFormQuestionConstructorWithParameters() {
        val options = listOf(
            CustomFormQuestionOption(id = 1, value = "Option 1"),
            CustomFormQuestionOption(id = 2, value = "Option 2")
        )
        
        val question = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            isMandatory = true,
            isMasked = false,
            position = 1,
            question = "What is your name?",
            isMultiselect = false,
            placeholder = "Enter your name",
            contentType = "text/plain",
            characterLimit = 100,
            helpText = "Please enter your full name",
            toggleOnText = "Yes",
            toggleOffText = "No",
            options = options
        )
        
        assertEquals(123, question.id)
        assertEquals(CustomFormQuestionType.TextEntry, question.type)
        assertEquals(true, question.isMandatory)
        assertEquals(false, question.isMasked)
        assertEquals(1, question.position)
        assertEquals("What is your name?", question.question)
        assertEquals(false, question.isMultiselect)
        assertEquals("Enter your name", question.placeholder)
        assertEquals("text/plain", question.contentType)
        assertEquals(100, question.characterLimit)
        assertEquals("Please enter your full name", question.helpText)
        assertEquals("Yes", question.toggleOnText)
        assertEquals("No", question.toggleOffText)
        assertEquals(options, question.options)
        assertNull(question.answer)
    }

    @Test
    fun customFormQuestionNullValues() {
        val question = CustomFormQuestion(
            id = null,
            type = CustomFormQuestionType.None,
            isMandatory = null,
            isMasked = null,
            position = null,
            question = null,
            isMultiselect = null,
            placeholder = null,
            contentType = null,
            characterLimit = null,
            helpText = null,
            toggleOnText = null,
            toggleOffText = null,
            options = null
        )
        
        assertNull(question.id)
        assertEquals(CustomFormQuestionType.None, question.type)
        assertNull(question.isMandatory)
        assertNull(question.isMasked)
        assertNull(question.position)
        assertNull(question.question)
        assertNull(question.isMultiselect)
        assertNull(question.placeholder)
        assertNull(question.contentType)
        assertNull(question.characterLimit)
        assertNull(question.helpText)
        assertNull(question.toggleOnText)
        assertNull(question.toggleOffText)
        assertNull(question.options)
    }

    @Test
    fun customFormQuestionEmptyStrings() {
        val question = CustomFormQuestion(
            id = 0,
            question = "",
            placeholder = "",
            contentType = "",
            helpText = "",
            toggleOnText = "",
            toggleOffText = ""
        )
        
        assertEquals(0, question.id)
        assertEquals("", question.question)
        assertEquals("", question.placeholder)
        assertEquals("", question.contentType)
        assertEquals("", question.helpText)
        assertEquals("", question.toggleOnText)
        assertEquals("", question.toggleOffText)
    }

    @Test
    fun customFormQuestionDifferentQuestionTypes() {
        val textQuestion = CustomFormQuestion(type = CustomFormQuestionType.TextEntry)
        val listQuestion = CustomFormQuestion(type = CustomFormQuestionType.ListPicker)
        val dateQuestion = CustomFormQuestion(type = CustomFormQuestionType.Date)
        val timeQuestion = CustomFormQuestion(type = CustomFormQuestionType.Time)
        val toggleQuestion = CustomFormQuestion(type = CustomFormQuestionType.Toggle)
        
        assertEquals(CustomFormQuestionType.TextEntry, textQuestion.type)
        assertEquals(CustomFormQuestionType.ListPicker, listQuestion.type)
        assertEquals(CustomFormQuestionType.Date, dateQuestion.type)
        assertEquals(CustomFormQuestionType.Time, timeQuestion.type)
        assertEquals(CustomFormQuestionType.Toggle, toggleQuestion.type)
    }

    @Test
    fun customFormQuestionBooleanValues() {
        val mandatoryQuestion = CustomFormQuestion(isMandatory = true, isMasked = false, isMultiselect = true)
        val optionalQuestion = CustomFormQuestion(isMandatory = false, isMasked = true, isMultiselect = false)
        
        assertEquals(true, mandatoryQuestion.isMandatory)
        assertEquals(false, mandatoryQuestion.isMasked)
        assertEquals(true, mandatoryQuestion.isMultiselect)
        
        assertEquals(false, optionalQuestion.isMandatory)
        assertEquals(true, optionalQuestion.isMasked)
        assertEquals(false, optionalQuestion.isMultiselect)
    }

    @Test
    fun customFormQuestionNegativeValues() {
        val question = CustomFormQuestion(
            id = -1,
            position = -5,
            characterLimit = -10
        )
        
        assertEquals(-1, question.id)
        assertEquals(-5, question.position)
        assertEquals(-10, question.characterLimit)
    }

    @Test
    fun customFormQuestionLargeValues() {
        val question = CustomFormQuestion(
            id = Int.MAX_VALUE,
            position = Int.MAX_VALUE,
            characterLimit = Int.MAX_VALUE,
            question = "Q".repeat(1000),
            placeholder = "P".repeat(500),
            helpText = "H".repeat(2000)
        )
        
        assertEquals(Int.MAX_VALUE, question.id)
        assertEquals(Int.MAX_VALUE, question.position)
        assertEquals(Int.MAX_VALUE, question.characterLimit)
        assertEquals("Q".repeat(1000), question.question)
        assertEquals("P".repeat(500), question.placeholder)
        assertEquals("H".repeat(2000), question.helpText)
    }

    @Test
    fun customFormQuestionEmptyOptionsList() {
        val question = CustomFormQuestion(options = emptyList())
        
        assertNotNull(question.options)
        assertTrue(question.options!!.isEmpty())
    }

    @Test
    fun customFormQuestionMultipleOptions() {
        val options = listOf(
            CustomFormQuestionOption(id = 1, value = "Option 1"),
            CustomFormQuestionOption(id = 2, value = "Option 2"),
            CustomFormQuestionOption(id = 3, value = "Option 3")
        )
        
        val question = CustomFormQuestion(options = options)
        
        assertEquals(3, question.options!!.size)
        assertEquals("Option 1", question.options!![0].value)
        assertEquals("Option 2", question.options!![1].value)
        assertEquals("Option 3", question.options!![2].value)
    }

    @Test
    fun customFormQuestionAnswerProperty() {
        val question = CustomFormQuestion()
        
        assertNull(question.answer)
        
        question.answer = CustomFormAnswer.Text("Sample answer")
        assertNotNull(question.answer)
        assertEquals(CustomFormAnswer.Text("Sample answer"), question.answer)
        
        question.answer = null
        assertNull(question.answer)
    }

    @Test
    fun customFormQuestionJsonSerialization() {
        val question = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            isMandatory = true,
            question = "Test Question",
            placeholder = "Test Placeholder"
        )
        
        val json = GsonProvider.gson.toJson(question)
        
        assertNotNull(json)
        assertTrue(json.contains("\"id\":123"))
        assertTrue(json.contains("\"type\":\"text_entry\""))
        assertTrue(json.contains("\"is_mandatory\":true"))
        assertTrue(json.contains("\"question\":\"Test Question\""))
        assertTrue(json.contains("\"placeholder\":\"Test Placeholder\""))
    }

    @Test
    fun customFormQuestionJsonSerializationWithNulls() {
        val question = CustomFormQuestion()
        
        val json = GsonProvider.gson.toJson(question)
        
        assertNotNull(json)
        assertTrue(json.contains("\"type\":\"\""))
    }

    @Test
    fun customFormQuestionJsonDeserialization() {
        val json = """
            {
                "id": 123,
                "type": "text_entry",
                "is_mandatory": true,
                "is_masked": false,
                "position": 1,
                "question": "Test Question",
                "is_multiselect": false,
                "placeholder": "Test Placeholder",
                "content_type": "text/plain",
                "character_limit": 100,
                "help_text": "Help text",
                "toggle_on_text": "Yes",
                "toggle_off_text": "No"
            }
        """.trimIndent()
        
        val question = GsonProvider.gson.fromJson(json, CustomFormQuestion::class.java)
        
        assertNotNull(question)
        assertEquals(123, question.id)
        assertEquals(CustomFormQuestionType.TextEntry, question.type)
        assertEquals(true, question.isMandatory)
        assertEquals(false, question.isMasked)
        assertEquals(1, question.position)
        assertEquals("Test Question", question.question)
        assertEquals(false, question.isMultiselect)
        assertEquals("Test Placeholder", question.placeholder)
        assertEquals("text/plain", question.contentType)
        assertEquals(100, question.characterLimit)
        assertEquals("Help text", question.helpText)
        assertEquals("Yes", question.toggleOnText)
        assertEquals("No", question.toggleOffText)
    }

    @Test
    fun customFormQuestionJsonDeserializationWithMissingFields() {
        val json = """
            {
                "id": 123,
                "question": "Test Question"
            }
        """.trimIndent()
        
        val question = GsonProvider.gson.fromJson(json, CustomFormQuestion::class.java)
        
        assertNotNull(question)
        assertEquals(123, question.id)
        assertEquals("Test Question", question.question)
        assertEquals(CustomFormQuestionType.None, question.type)
        assertNull(question.isMandatory)
        assertNull(question.position)
    }

    @Test
    fun customFormQuestionJsonDeserializationWithOptions() {
        val json = """
            {
                "id": 123,
                "type": "list_picker",
                "question": "Choose an option",
                "options": [
                    {"id": 1, "value": "Option 1"},
                    {"id": 2, "value": "Option 2"}
                ]
            }
        """.trimIndent()
        
        val question = GsonProvider.gson.fromJson(json, CustomFormQuestion::class.java)
        
        assertNotNull(question)
        assertEquals(123, question.id)
        assertEquals(CustomFormQuestionType.ListPicker, question.type)
        assertEquals("Choose an option", question.question)
        assertNotNull(question.options)
        assertEquals(2, question.options!!.size)
        assertEquals("Option 1", question.options!![0].value)
        assertEquals("Option 2", question.options!![1].value)
    }

    @Test
    fun customFormQuestionJsonRoundTrip() {
        val originalQuestion = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            isMandatory = true,
            question = "Test Question",
            placeholder = "Test Placeholder"
        )
        
        val json = GsonProvider.gson.toJson(originalQuestion)
        val deserializedQuestion = GsonProvider.gson.fromJson(json, CustomFormQuestion::class.java)
        
        assertEquals(originalQuestion.id, deserializedQuestion.id)
        assertEquals(originalQuestion.type, deserializedQuestion.type)
        assertEquals(originalQuestion.isMandatory, deserializedQuestion.isMandatory)
        assertEquals(originalQuestion.question, deserializedQuestion.question)
        assertEquals(originalQuestion.placeholder, deserializedQuestion.placeholder)
    }

    @Test
    fun customFormQuestionJsonDeserializationWithInvalidJson() {
        val invalidJson = "{ invalid json }"
        assertThrows(JsonSyntaxException::class.java) {
            GsonProvider.gson.fromJson(invalidJson, CustomFormQuestion::class.java)
        }
    }

    @Test
    fun customFormQuestionEquals() {
        val question1 = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            question = "Test Question"
        )
        
        val question2 = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            question = "Test Question"
        )
        
        val question3 = CustomFormQuestion(
            id = 456,
            type = CustomFormQuestionType.ListPicker,
            question = "Different Question"
        )
        
        assertEquals(question1, question2)
        assertNotEquals(question1, question3)
        assertNotEquals(question1, null)
        assertNotEquals(question1, "not a CustomFormQuestion")
    }

    @Test
    fun customFormQuestionHashCode() {
        val question1 = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            question = "Test Question"
        )
        
        val question2 = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            question = "Test Question"
        )
        
        assertEquals(question1.hashCode(), question2.hashCode())
    }

    @Test
    fun customFormQuestionToString() {
        val question = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            question = "Test Question"
        )
        
        val toStringResult = question.toString()
        
        assertNotNull(toStringResult)
        assertTrue(toStringResult.contains("CustomFormQuestion"))
        assertTrue(toStringResult.contains("123"))
        assertTrue(toStringResult.contains("TextEntry"))
        assertTrue(toStringResult.contains("Test Question"))
    }

    @Test
    fun customFormQuestionCopy() {
        val originalQuestion = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            question = "Test Question"
        )
        
        val copiedQuestion = originalQuestion.copy()
        
        assertEquals(originalQuestion, copiedQuestion)
        assertNotSame(originalQuestion, copiedQuestion)
    }

    @Test
    fun customFormQuestionCopyWithChanges() {
        val originalQuestion = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            question = "Original Question",
            isMandatory = true
        )
        
        val modifiedQuestion = originalQuestion.copy(
            id = 456,
            question = "Modified Question"
        )
        
        assertEquals(456, modifiedQuestion.id)
        assertEquals("Modified Question", modifiedQuestion.question)
        assertEquals(CustomFormQuestionType.TextEntry, modifiedQuestion.type)
        assertEquals(true, modifiedQuestion.isMandatory)
    }

    @Test
    fun customFormQuestionSpecialCharacters() {
        val question = CustomFormQuestion(
            question = "Question with special chars !@#$%^&*()",
            placeholder = "Placeholder with symbols []{}()",
            helpText = "Help text with punctuation: semicolon; comma, period."
        )
        
        assertEquals("Question with special chars !@#$%^&*()", question.question)
        assertEquals("Placeholder with symbols []{}()", question.placeholder)
        assertEquals("Help text with punctuation: semicolon; comma, period.", question.helpText)
    }

    @Test
    fun customFormQuestionComponentFunctions() {
        val question = CustomFormQuestion(
            id = 123,
            type = CustomFormQuestionType.TextEntry,
            isMandatory = true,
            isMasked = false,
            position = 1,
            question = "Test Question"
        )
        
        val (id, type, isMandatory, isMasked, position, questionText) = question
        
        assertEquals(123, id)
        assertEquals(CustomFormQuestionType.TextEntry, type)
        assertEquals(true, isMandatory)
        assertEquals(false, isMasked)
        assertEquals(1, position)
        assertEquals("Test Question", questionText)
    }
}
