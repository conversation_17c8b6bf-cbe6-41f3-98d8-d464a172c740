package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.MediaType
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class FileUploadRequestTest {

    private val gson: Gson = GsonBuilder().create()

    @Test
    fun photoPathRequestConstruction() {
        val path = PhotoPathRequest(s3Path = "s3://bucket/photo.jpg", photoType = MediaType.Photo)
        assertEquals("s3://bucket/photo.jpg", path.s3Path)
        assertEquals(MediaType.Photo, path.photoType)
    }

    @Test
    fun photoRequestConstruction() {
        val path = PhotoPathRequest("s3://bucket/photo.jpg", MediaType.Photo)
        val request = PhotoRequest(
            photo = listOf(path),
            photoType = MediaType.Photo,
            multiple = true,
            smartActionId = 123
        )
        assertEquals(listOf(path), request.photo)
        assertEquals(MediaType.Photo, request.photoType)
        assertTrue(request.multiple)
        assertEquals(123, request.smartActionId)
    }

    @Test
    fun photoResponseConstruction() {
        val response = PhotoResponse(
            url = "http://example.com/photo.jpg",
            mediaId = 42,
            s3Path = "s3://bucket/photo.jpg"
        )
        assertEquals("http://example.com/photo.jpg", response.url)
        assertEquals(42, response.mediaId)
        assertEquals("s3://bucket/photo.jpg", response.s3Path)
    }

    @Test
    fun signedURLResponseConstruction() {
        val fields = mapOf("key" to "value", "token" to "abc")
        val response = SignedURLResponse(
            url = "http://signed.url",
            fields = fields
        )
        assertEquals("http://signed.url", response.url)
        assertEquals(fields, response.fields)
    }

    @Test
    fun gsonSerializationDeserialization() {
        val path = PhotoPathRequest("s3://bucket/photo.jpg", MediaType.Photo)
        val request = PhotoRequest(listOf(path), MediaType.Photo, false, 1)
        val json = gson.toJson(request)
        val fromJson = gson.fromJson(json, PhotoRequest::class.java)
        assertEquals(request, fromJson)
    }
}
