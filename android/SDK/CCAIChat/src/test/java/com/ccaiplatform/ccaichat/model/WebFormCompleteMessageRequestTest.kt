package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class WebFormCompleteMessageRequestTest {

    @Test
    fun constructorWithAllParameters() {
        val eventData = FormCompleteEventData(
            status = "success",
            timestamp = "2024-01-01T10:00:00Z",
            smartActionId = 123
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "test-signature",
            data = eventData
        )
        
        assertEquals(ChatMessageBodyType.FormComplete, request.type)
        assertEquals("test-signature", request.signature)
        assertEquals(eventData, request.data)
    }

    @Test
    fun constructorWithNullSignature() {
        val eventData = FormCompleteEventData(
            status = "success",
            smartActionId = 456
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = null,
            data = eventData
        )
        
        assertEquals(ChatMessageBodyType.FormComplete, request.type)
        assertNull(request.signature)
        assertEquals(eventData, request.data)
    }

    @Test
    fun constructorWithDefaultSignature() {
        val eventData = FormCompleteEventData(
            status = "failed",
            smartActionId = 789
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            data = eventData
        )
        
        assertEquals(ChatMessageBodyType.FormComplete, request.type)
        assertNull(request.signature)
        assertEquals(eventData, request.data)
    }

    @Test
    fun constructorWithComplexEventData() {
        val details = FormCompleteEventDetails(
            errorCode = "E001",
            message = "Form validation failed"
        )
        
        val eventData = FormCompleteEventData(
            status = "error",
            timestamp = "2024-01-01T15:30:00Z",
            smartActionId = 999,
            details = details
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "complex-signature-12345",
            data = eventData
        )
        
        assertEquals(ChatMessageBodyType.FormComplete, request.type)
        assertEquals("complex-signature-12345", request.signature)
        assertEquals(eventData, request.data)
        assertEquals("error", request.data.status)
        assertEquals(999, request.data.smartActionId)
        assertEquals("E001", request.data.details?.errorCode)
    }

    @Test
    fun constructorWithEmptySignature() {
        val eventData = FormCompleteEventData(
            status = "pending",
            smartActionId = 111
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "",
            data = eventData
        )
        
        assertEquals(ChatMessageBodyType.FormComplete, request.type)
        assertEquals("", request.signature)
        assertEquals(eventData, request.data)
    }

    @Test
    fun implementsChatMessageRequestInterface() {
        val eventData = FormCompleteEventData(status = "success", smartActionId = 123)
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            data = eventData
        )
        
        assertTrue(request is ChatMessageRequest)
        assertEquals(ChatMessageBodyType.FormComplete, request.type)
    }

    @Test
    fun toJSONStringReturnsValidJson() {
        val eventData = FormCompleteEventData(
            status = "success",
            timestamp = "2024-01-01T10:00:00Z",
            smartActionId = 123
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "test-signature",
            data = eventData
        )
        
        val json = request.toJSONString()
        assertNotNull(json)
        assertTrue(json!!.contains("\"type\""))
        assertTrue(json.contains("\"signature\""))
        assertTrue(json.contains("\"data\""))
        assertTrue(json.contains("test-signature"))
    }

    @Test
    fun toJSONStringWithNullSignature() {
        val eventData = FormCompleteEventData(
            status = "success",
            smartActionId = 456
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = null,
            data = eventData
        )
        
        val json = request.toJSONString()
        assertNotNull(json)
        assertTrue(json!!.contains("\"data\""))
    }

    @Test
    fun dataClassEquality() {
        val eventData = FormCompleteEventData(
            status = "success",
            smartActionId = 123
        )
        
        val request1 = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "same-signature",
            data = eventData
        )
        
        val request2 = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "same-signature",
            data = eventData
        )
        
        assertEquals(request1, request2)
        assertEquals(request1.hashCode(), request2.hashCode())
    }

    @Test
    fun dataClassInequality() {
        val eventData1 = FormCompleteEventData(status = "success", smartActionId = 123)
        val eventData2 = FormCompleteEventData(status = "failed", smartActionId = 456)
        
        val request1 = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "signature1",
            data = eventData1
        )
        
        val request2 = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "signature2",
            data = eventData2
        )
        
        assertTrue(request1 != request2)
    }

    @Test
    fun dataClassCopy() {
        val eventData = FormCompleteEventData(
            status = "success",
            smartActionId = 123
        )
        
        val original = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "original-signature",
            data = eventData
        )
        
        val copied = original.copy(signature = "copied-signature")
        
        assertEquals(ChatMessageBodyType.FormComplete, copied.type)
        assertEquals("copied-signature", copied.signature)
        assertEquals(eventData, copied.data)
        assertEquals(original.type, copied.type)
    }

    @Test
    fun dataClassToString() {
        val eventData = FormCompleteEventData(
            status = "success",
            smartActionId = 123
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "test-signature",
            data = eventData
        )
        
        val toString = request.toString()
        assertNotNull(toString)
        assertTrue(toString.contains("WebFormCompleteMessageRequest"))
        assertTrue(toString.contains("test-signature"))
    }

    @Test
    fun dataClassDestructuring() {
        val eventData = FormCompleteEventData(
            status = "completed",
            smartActionId = 789
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = "destructure-signature",
            data = eventData
        )
        
        val (type, signature, data) = request
        assertEquals(ChatMessageBodyType.FormComplete, type)
        assertEquals("destructure-signature", signature)
        assertEquals(eventData, data)
    }

    @Test
    fun withDifferentFormStatuses() {
        val statuses = listOf("success", "failed", "pending", "error", "timeout")
        
        statuses.forEach { status ->
            val eventData = FormCompleteEventData(
                status = status,
                smartActionId = 100
            )
            
            val request = WebFormCompleteMessageRequest(
                type = ChatMessageBodyType.FormComplete,
                signature = "test-$status",
                data = eventData
            )
            
            assertEquals(ChatMessageBodyType.FormComplete, request.type)
            assertEquals("test-$status", request.signature)
            assertEquals(status, request.data.status)
        }
    }

    @Test
    fun withLongSignature() {
        val longSignature = "very-long-signature-".repeat(50) + "end"
        val eventData = FormCompleteEventData(
            status = "success",
            smartActionId = 123
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = longSignature,
            data = eventData
        )
        
        assertEquals(ChatMessageBodyType.FormComplete, request.type)
        assertEquals(longSignature, request.signature)
        assertTrue(request.signature!!.length > 500)
    }

    @Test
    fun withSpecialCharactersInSignature() {
        val specialSignature = "signature-with-special-chars-and-symbols!@#$%^&*()"
        val eventData = FormCompleteEventData(
            status = "success",
            smartActionId = 123
        )
        
        val request = WebFormCompleteMessageRequest(
            type = ChatMessageBodyType.FormComplete,
            signature = specialSignature,
            data = eventData
        )
        
        assertEquals(ChatMessageBodyType.FormComplete, request.type)
        assertEquals(specialSignature, request.signature)
    }
}
