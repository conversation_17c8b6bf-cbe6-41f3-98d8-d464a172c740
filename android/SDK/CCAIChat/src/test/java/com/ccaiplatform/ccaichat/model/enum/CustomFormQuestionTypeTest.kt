package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class CustomFormQuestionTypeTest {

    private val gson: Gson = GsonBuilder()
        .registerTypeAdapter(CustomFormQuestionType::class.java, CustomFormQuestionTypeAdapter())
        .create()

    @Test
    fun fromValueReturnsCorrectEnum() {
        assertEquals(CustomFormQuestionType.TextEntry, CustomFormQuestionType.fromValue("text_entry"))
        assertEquals(CustomFormQuestionType.ListPicker, CustomFormQuestionType.fromValue("list_picker"))
        assertEquals(CustomFormQuestionType.Date, CustomFormQuestionType.fromValue("date"))
        assertEquals(CustomFormQuestionType.Time, CustomFormQuestionType.fromValue("time"))
        assertEquals(CustomFormQuestionType.Toggle, CustomFormQuestionType.fromValue("toggle"))
        assertEquals(CustomFormQuestionType.None, CustomFormQuestionType.fromValue(""))
        assertEquals(CustomFormQuestionType.None, CustomFormQuestionType.fromValue("unknown"))
    }

    @Test
    fun serializeEnumToJson() {
        assertEquals("\"text_entry\"", gson.toJson(CustomFormQuestionType.TextEntry))
        assertEquals("\"list_picker\"", gson.toJson(CustomFormQuestionType.ListPicker))
        assertEquals("\"date\"", gson.toJson(CustomFormQuestionType.Date))
        assertEquals("\"time\"", gson.toJson(CustomFormQuestionType.Time))
        assertEquals("\"toggle\"", gson.toJson(CustomFormQuestionType.Toggle))
        assertEquals("\"\"", gson.toJson(CustomFormQuestionType.None))
    }

    @Test
    fun deserializeJsonToEnum() {
        assertEquals(CustomFormQuestionType.TextEntry, gson.fromJson("\"text_entry\"", CustomFormQuestionType::class.java))
        assertEquals(CustomFormQuestionType.ListPicker, gson.fromJson("\"list_picker\"", CustomFormQuestionType::class.java))
        assertEquals(CustomFormQuestionType.Date, gson.fromJson("\"date\"", CustomFormQuestionType::class.java))
        assertEquals(CustomFormQuestionType.Time, gson.fromJson("\"time\"", CustomFormQuestionType::class.java))
        assertEquals(CustomFormQuestionType.Toggle, gson.fromJson("\"toggle\"", CustomFormQuestionType::class.java))
        assertEquals(CustomFormQuestionType.None, gson.fromJson("\"\"", CustomFormQuestionType::class.java))
        assertEquals(CustomFormQuestionType.None, gson.fromJson("\"unknown\"", CustomFormQuestionType::class.java))
    }
}
