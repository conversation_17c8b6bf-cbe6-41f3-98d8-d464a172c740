package com.ccaiplatform.ccaichat.model

import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ChatTaskVaMessageTest {
    @Test
    fun chatTaskVaMessageCreation() {
        val body = ChatMessageBody(content = "test-content")
        val msg = ChatTaskVaMessage(
            content = body,
            id = 1,
            taskVaId = 2,
            participantId = 3
        )
        assertEquals(body, msg.content)
        assertEquals(1, msg.id)
        assertEquals(2, msg.taskVaId)
        assertEquals(3, msg.participantId)
    }

    @Test
    fun chatTaskVaMessageEquality() {
        val body = ChatMessageBody(content = "test-content")
        val msg1 = ChatTaskVaMessage(body, 1, 2, 3)
        val msg2 = ChatTaskVaMessage(body, 1, 2, 3)
        assertEquals(msg1, msg2)
        assertEquals(msg1.hashCode(), msg2.hashCode())
    }

    @Test
    fun chatTaskVaMessageInequality() {
        val body1 = ChatMessageBody(content = "test-content-1")
        val body2 = ChatMessageBody(content = "test-content-2")
        val msg1 = ChatTaskVaMessage(body1, 1, 2, 3)
        val msg2 = ChatTaskVaMessage(body2, 4, 5, 6)
        assertNotEquals(msg1, msg2)
    }

    @Test
    fun chatTaskVaMessageSerializationDeserialization() {
        val body = ChatMessageBody(content = "test-content")
        val msg = ChatTaskVaMessage(body, 1, 2, 3)
        val gson: Gson = GsonBuilder().create()
        val json = gson.toJson(msg)
        val deserialized = gson.fromJson(json, ChatTaskVaMessage::class.java)
        assertEquals(msg, deserialized)
        assertTrue(json.contains("\"content\""))
        assertTrue(json.contains("\"id\""))
        assertTrue(json.contains("\"task_va_id\""))
        assertTrue(json.contains("\"participant_id\""))
    }

    @Test
    fun chatTaskVaMessageConvertToChatMessage() {
        val body = ChatMessageBody(content = "test-content")
        val msg = ChatTaskVaMessage(body, 1, 2, 3)
        val chatMessage = msg.convertToChatMessage()
        assertEquals(msg.id, chatMessage.id)
        assertEquals(msg.taskVaId, chatMessage.taskVaId)
        assertEquals(msg.participantId, chatMessage.participantId)
        assertEquals(body.content, chatMessage.body.content)
    }

    @Test
    fun chatTaskVaMessageWithSpecialCharacters() {
        val special = "!@#￥%……&*（）——+🙂🚀\"'"
        val body = ChatMessageBody(content = special)
        val msg = ChatTaskVaMessage(body, 1, 2, 3)
        val gson: Gson = GsonBuilder().create()
        val json = gson.toJson(msg)
        val deserialized = gson.fromJson(json, ChatTaskVaMessage::class.java)
        assertEquals(msg, deserialized)
    }

    @Test
    fun chatTaskVaMessageToString() {
        val body = ChatMessageBody(content = "test-content")
        val msg = ChatTaskVaMessage(body, 1, 2, 3)
        assert(msg.toString().isNotEmpty())
    }

    @Test
    fun chatTaskVaMessageDecodingFromJson() {
        val json = """
            {
                "content": {
                    "type": "text",
                    "content": "hello json"
                },
                "id": 10,
                "task_va_id": 20,
                "participant_id": 30
            }
        """.trimIndent()
        val gson: Gson = GsonBuilder().create()
        val decoded = gson.fromJson(json, ChatTaskVaMessage::class.java)
        assertEquals(10, decoded.id)
        assertEquals(20, decoded.taskVaId)
        assertEquals(30, decoded.participantId)
        assertEquals("hello json", decoded.content.content)
        assertEquals(ChatMessageBodyType.Text, decoded.content.type)
    }
}
