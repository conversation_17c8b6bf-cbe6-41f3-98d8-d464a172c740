package com.ccaiplatform.ccaichat.model

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ButtonActionTest {

    @Test
    fun shouldCreateButtonAction() {
        val action = ButtonAction.QuickReply
        assertEquals("quick_reply", action.value)
    }

    @Test
    fun shouldCreateButtonActionWithEscalation() {
        val action = ButtonAction.Escalation
        assertEquals("escalation", action.value)
    }

    @Test
    fun shouldCreateButtonActionWithQuickReplyLink() {
        val action = ButtonAction.QuickReplyLink
        assertEquals("quick_reply_link", action.value)
    }

    @Test
    fun shouldCreateButtonActionWithExternalDeflectionLink() {
        val action = ButtonAction.ExternalDeflectionLink
        assertEquals("external_deflection_link", action.value)
    }

    @Test
    fun shouldCreateButtonActionWithContinueVirtualAgent() {
        val action = ButtonAction.ContinueVirtualAgent
        assertEquals("virtualAgent", action.value)
    }

    @Test
    fun shouldCreateButtonActionWithEmail() {
        val action = ButtonAction.Email
        assertEquals("email", action.value)
    }

    @Test
    fun shouldCreateButtonActionWithEndChat() {
        val action = ButtonAction.EndChat
        assertEquals("end_chat", action.value)
    }
}
