package com.ccaiplatform.ccaichat

import com.ccaiplatform.ccaichat.model.WebFormResponse
import com.ccaiplatform.ccaichat.model.WebFormRequest

/**
 * Interface for handling web form requests.
 */
interface CCAIWebFormInterface {
    /**
     * Handles a web form request and returns a response.
     * This method is called when a web form is requested during a chat session.
     * If the request is successfully processed, return a `WebFormResponse` with the relevant data.
     * If the request cannot be processed, return null to indicate that no form is available.
     * This method is typically used to handle form requests that require server-side processing,
     * such as validating form data using signature. Implement this method in your app to handle
     * web form requests appropriately.
     *
     * @param request The web form request to process.
     * @return The response to the web form request, or null if not handled.
     */
    suspend fun handleWebFormRequest(request: WebFormRequest): WebFormResponse?
}
