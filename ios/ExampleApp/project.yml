name: ExampleApp
options:
  deploymentTarget:
    iOS: 16.6
packages:
  CCAIKit:
    path: ../../spm
settingGroups:
  targetedDeviceFamiliy:
    TARGETED_DEVICE_FAMILY: 1
settings:
  base:
    CODE_SIGN_STYLE: Manual
    CURRENT_PROJECT_VERSION: 1
    DEBUG_INFORMATION_FORMAT: "dwarf-with-dsym"
    DEVELOPMENT_TEAM: 8694345EB8
    GENERATE_INFOPLIST_FILE: true
    MARKETING_VERSION: 3.0.0
    PRODUCT_BUNDLE_IDENTIFIER: co.ujet.qa.ExampleApp
  configs:
    debug:
      PROVISIONING_PROFILE_SPECIFIER: match Development co.ujet.qa.ExampleApp
    release:
      PROVISIONING_PROFILE_SPECIFIER: match AppStore co.ujet.qa.ExampleApp
targets:
  Empty:
    templates: [App]
    
  Chat:
    templates: [App]
    dependencies:
      - package: CCAIKit
      - target: screenshare-extension
        embed: true
        codeSign: false
        buildPhase:
          copyFiles:
            destination: plugins
  
  ChatSwiftUI:
    templates: [App]
    dependencies:
      - package: CCAIKit
      - target: screenshare-extension
        embed: true
        codeSign: false
        buildPhase:
          copyFiles:
            destination: plugins

  screenshare-extension:
    templates: [Extension]
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: co.ujet.qa.ExampleApp.ScreenShareExtension
      configs:
        debug:
          PROVISIONING_PROFILE_SPECIFIER: match Development co.ujet.qa.ExampleApp.ScreenShareExtension
        release:
          PROVISIONING_PROFILE_SPECIFIER: match AppStore co.ujet.qa.ExampleApp.ScreenShareExtension
    
targetTemplates:
  App:
    type: application
    platform: iOS
    scheme: {}
    sources:
      - path: ../ExampleApp//${target_name}
    info:
      path: ../ios/ExampleApp//Info.plist
      properties:
        CBIOBroadcastExtension: co.ujet.qa.ExampleApp.ScreenShareExtension
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleURLTypes:
          - CFBundleTypeRole: Editor
            CFBundleURLName: co.ujet.qa.ExampleApp
            CFBundleURLSchemes:
              - ccai
        CFBundleVersion: 1
        NSCameraUsageDescription: "Camera Usage Description"
        NSFaceIDUsageDescription: "FaceID Usage Description"
        NSMicrophoneUsageDescription: "Microphone Usage Description"
        NSPhotoLibraryUsageDescription: "Photo Library Usage Description"
        NSBluetoothAlwaysUsageDescription: "Bluetooth Usage Description"
        NSLocationWhenInUseUsageDescription: "Location Usage Description"
        UIBackgroundModes:
          - audio
          - remote-notification
          - voip
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
    entitlements:
      path: ../ios/ExampleApp//app.entitlements
      properties:
        aps-environment: development
        keychain-access-groups: 
          - $(AppIdentifierPrefix)io.cobrowse

  Extension:
    type: app-extension
    platform: iOS
    sources:
      - path: ../ScreenShareExtension
        excludes:
          - Info.plist
    info:
      path: ../ios/ExampleApp//Info-${target_name}.plist
      properties:
        CFBundleDisplayName: ScreenShareExtension
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleVersion: 1
        NSExtension:
          NSExtensionPointIdentifier: com.apple.broadcast-services-upload
          NSExtensionPrincipalClass: SampleHandler
          RPBroadcastProcessMode: RPBroadcastProcessModeSampleBuffer
    entitlements:
      path: ../ios/ExampleApp//${target_name}.entitlements
      properties:
        keychain-access-groups: 
          - $(AppIdentifierPrefix)io.cobrowse
