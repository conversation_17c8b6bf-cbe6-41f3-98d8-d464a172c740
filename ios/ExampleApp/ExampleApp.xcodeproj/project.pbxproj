// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 63;
	objects = {

/* Begin PBXBuildFile section */
		0761091304A3E083B63BB729 /* CCAIKit in Frameworks */ = {isa = PBXBuildFile; productRef = 388BDCC85C64693362F5562F /* CCAIKit */; };
		1515C2C688F1E4607FC36351 /* SampleHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CB618D938562141671DA878 /* SampleHandler.swift */; };
		1B8EDFBA46DEA98DCC87BCA4 /* ExampleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185CD459BBFCFEA5F0058578 /* ExampleApp.swift */; };
		40DC82003BFF86BC06774F08 /* screenshare-extension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 04855BDCD6E2C95DF6CA40CE /* screenshare-extension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		53FDFF2239824AE9F6B374B6 /* CCAIKit in Frameworks */ = {isa = PBXBuildFile; productRef = 12A634332E758F6456D249FC /* CCAIKit */; };
		5A1FFC39972506BEA2EB7DF9 /* ExampleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 003E8871734FED0E3B786D32 /* ExampleApp.swift */; };
		95029BAA32E9FFFA349DFC7A /* screenshare-extension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 04855BDCD6E2C95DF6CA40CE /* screenshare-extension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		EC539771280CD7A4C56D3E11 /* ExampleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 43FBAFA585801F846D040CB3 /* ExampleApp.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7D662A663A2369485B616838 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E6FC2ABCE62557C86BAC1580 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 56E73DDE141BC0188973BBAA;
			remoteInfo = "screenshare-extension";
		};
		F66780091A7E4E73064DE4F0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E6FC2ABCE62557C86BAC1580 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 56E73DDE141BC0188973BBAA;
			remoteInfo = "screenshare-extension";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		E6E6C5C347BA4F557900D638 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				95029BAA32E9FFFA349DFC7A /* screenshare-extension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F74CBDE41DB8CEBD4A6E5314 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				40DC82003BFF86BC06774F08 /* screenshare-extension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		003E8871734FED0E3B786D32 /* ExampleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExampleApp.swift; sourceTree = "<group>"; };
		04855BDCD6E2C95DF6CA40CE /* screenshare-extension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "screenshare-extension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		185CD459BBFCFEA5F0058578 /* ExampleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExampleApp.swift; sourceTree = "<group>"; };
		1A05CC0359ED66FA97F6360F /* Chat.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Chat.app; sourceTree = BUILT_PRODUCTS_DIR; };
		357283BC046256C97B29A2E0 /* Empty.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Empty.app; sourceTree = BUILT_PRODUCTS_DIR; };
		43FBAFA585801F846D040CB3 /* ExampleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExampleApp.swift; sourceTree = "<group>"; };
		6CB618D938562141671DA878 /* SampleHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SampleHandler.swift; sourceTree = "<group>"; };
		73D3143CC585B7369CE33201 /* ScreenShareExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = ScreenShareExtension.entitlements; sourceTree = "<group>"; };
		AE95AE600FBE62A1907E0DC8 /* ChatSwiftUI.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ChatSwiftUI.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C4720969CBD07A841B1E252A /* spm */ = {isa = PBXFileReference; lastKnownFileType = folder; name = spm; path = ../../spm; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		162913FCDDB7B5DFA4E386D2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				53FDFF2239824AE9F6B374B6 /* CCAIKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A094867C745BFA3453B61FE2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0761091304A3E083B63BB729 /* CCAIKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		35682931423AD5E2958D6C46 /* Chat */ = {
			isa = PBXGroup;
			children = (
				43FBAFA585801F846D040CB3 /* ExampleApp.swift */,
			);
			path = Chat;
			sourceTree = "<group>";
		};
		5ADE33FF27207431D453BDE1 /* ChatSwiftUI */ = {
			isa = PBXGroup;
			children = (
				003E8871734FED0E3B786D32 /* ExampleApp.swift */,
			);
			path = ChatSwiftUI;
			sourceTree = "<group>";
		};
		AF4E1C376012F79EC7DF8379 /* Products */ = {
			isa = PBXGroup;
			children = (
				1A05CC0359ED66FA97F6360F /* Chat.app */,
				AE95AE600FBE62A1907E0DC8 /* ChatSwiftUI.app */,
				357283BC046256C97B29A2E0 /* Empty.app */,
				04855BDCD6E2C95DF6CA40CE /* screenshare-extension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B12CF7AB1C92C5D2AAB3FE6A /* ScreenShareExtension */ = {
			isa = PBXGroup;
			children = (
				6CB618D938562141671DA878 /* SampleHandler.swift */,
				73D3143CC585B7369CE33201 /* ScreenShareExtension.entitlements */,
			);
			name = ScreenShareExtension;
			path = ../ScreenShareExtension;
			sourceTree = "<group>";
		};
		E5988D9AE80AB7E96D0259F6 /* Packages */ = {
			isa = PBXGroup;
			children = (
				C4720969CBD07A841B1E252A /* spm */,
			);
			name = Packages;
			sourceTree = "<group>";
		};
		ECA11DCDB4BE2265B358A0E2 = {
			isa = PBXGroup;
			children = (
				35682931423AD5E2958D6C46 /* Chat */,
				5ADE33FF27207431D453BDE1 /* ChatSwiftUI */,
				ED2A06E1CFC443BF52E1FF95 /* Empty */,
				E5988D9AE80AB7E96D0259F6 /* Packages */,
				B12CF7AB1C92C5D2AAB3FE6A /* ScreenShareExtension */,
				AF4E1C376012F79EC7DF8379 /* Products */,
			);
			sourceTree = "<group>";
		};
		ED2A06E1CFC443BF52E1FF95 /* Empty */ = {
			isa = PBXGroup;
			children = (
				185CD459BBFCFEA5F0058578 /* ExampleApp.swift */,
			);
			path = Empty;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		056CF8A4DDA828197FFE6C84 /* Chat */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E20B7A97B34D5BD9877BF1C4 /* Build configuration list for PBXNativeTarget "Chat" */;
			buildPhases = (
				B886D8C55E1EBA851583C921 /* Sources */,
				A094867C745BFA3453B61FE2 /* Frameworks */,
				E6E6C5C347BA4F557900D638 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				876BA718202351D493A5C82D /* PBXTargetDependency */,
			);
			name = Chat;
			packageProductDependencies = (
				388BDCC85C64693362F5562F /* CCAIKit */,
			);
			productName = Chat;
			productReference = 1A05CC0359ED66FA97F6360F /* Chat.app */;
			productType = "com.apple.product-type.application";
		};
		56E73DDE141BC0188973BBAA /* screenshare-extension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D351009F7512CB3363630CDB /* Build configuration list for PBXNativeTarget "screenshare-extension" */;
			buildPhases = (
				F559FD8F6BE8446424A7EBFA /* Sources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "screenshare-extension";
			packageProductDependencies = (
			);
			productName = "screenshare-extension";
			productReference = 04855BDCD6E2C95DF6CA40CE /* screenshare-extension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		5A9410617FB68EA85105BFAF /* Empty */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5DB548D70D1BBC8AA92C9F0C /* Build configuration list for PBXNativeTarget "Empty" */;
			buildPhases = (
				D22D968C62FC15A24632F2D3 /* Sources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Empty;
			packageProductDependencies = (
			);
			productName = Empty;
			productReference = 357283BC046256C97B29A2E0 /* Empty.app */;
			productType = "com.apple.product-type.application";
		};
		8DB66732DC5F4AC7E0E10D83 /* ChatSwiftUI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C8B23987A45B2F4F51621AA0 /* Build configuration list for PBXNativeTarget "ChatSwiftUI" */;
			buildPhases = (
				308BA93C7F7656B4A9B96A0F /* Sources */,
				162913FCDDB7B5DFA4E386D2 /* Frameworks */,
				F74CBDE41DB8CEBD4A6E5314 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				F4DA9D3B672DF08F0A35D5F3 /* PBXTargetDependency */,
			);
			name = ChatSwiftUI;
			packageProductDependencies = (
				12A634332E758F6456D249FC /* CCAIKit */,
			);
			productName = ChatSwiftUI;
			productReference = AE95AE600FBE62A1907E0DC8 /* ChatSwiftUI.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E6FC2ABCE62557C86BAC1580 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
					056CF8A4DDA828197FFE6C84 = {
						DevelopmentTeam = 8694345EB8;
						ProvisioningStyle = Manual;
					};
					56E73DDE141BC0188973BBAA = {
						DevelopmentTeam = 8694345EB8;
						ProvisioningStyle = Manual;
					};
					5A9410617FB68EA85105BFAF = {
						DevelopmentTeam = 8694345EB8;
						ProvisioningStyle = Manual;
					};
					8DB66732DC5F4AC7E0E10D83 = {
						DevelopmentTeam = 8694345EB8;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = 73A70D16F69D798FC49217D0 /* Build configuration list for PBXProject "ExampleApp" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = ECA11DCDB4BE2265B358A0E2;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				4E23964B16344D529DF143E2 /* XCLocalSwiftPackageReference "../../spm" */,
			);
			projectDirPath = "";
			projectRoot = "";
			targets = (
				056CF8A4DDA828197FFE6C84 /* Chat */,
				8DB66732DC5F4AC7E0E10D83 /* ChatSwiftUI */,
				5A9410617FB68EA85105BFAF /* Empty */,
				56E73DDE141BC0188973BBAA /* screenshare-extension */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		308BA93C7F7656B4A9B96A0F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5A1FFC39972506BEA2EB7DF9 /* ExampleApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B886D8C55E1EBA851583C921 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EC539771280CD7A4C56D3E11 /* ExampleApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D22D968C62FC15A24632F2D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1B8EDFBA46DEA98DCC87BCA4 /* ExampleApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F559FD8F6BE8446424A7EBFA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1515C2C688F1E4607FC36351 /* SampleHandler.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		876BA718202351D493A5C82D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 56E73DDE141BC0188973BBAA /* screenshare-extension */;
			targetProxy = 7D662A663A2369485B616838 /* PBXContainerItemProxy */;
		};
		F4DA9D3B672DF08F0A35D5F3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 56E73DDE141BC0188973BBAA /* screenshare-extension */;
			targetProxy = F66780091A7E4E73064DE4F0 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		196EB923FC4ABC15C3C9953A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = ../ios/ExampleApp/app.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				INFOPLIST_FILE = ../ios/ExampleApp/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1EFA7DE0C166B5B12443A164 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 8694345EB8;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				MARKETING_VERSION = 3.0.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = co.ujet.qa.ExampleApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore co.ujet.qa.ExampleApp";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		2AEBFEA520CB213A66FF7828 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = ../ios/ExampleApp/app.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				INFOPLIST_FILE = ../ios/ExampleApp/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		591C02B226A2274D5CAB7688 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = ../ios/ExampleApp/app.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				INFOPLIST_FILE = ../ios/ExampleApp/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6FAA55CE68DD1B20DAE3DD29 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = "../ios/ExampleApp/screenshare-extension.entitlements";
				INFOPLIST_FILE = "../ios/ExampleApp/Info-screenshare-extension.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = co.ujet.qa.ExampleApp.ScreenShareExtension;
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore co.ujet.qa.ExampleApp.ScreenShareExtension";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		9B5FB642D3B8446BB52E1046 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = ../ios/ExampleApp/app.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				INFOPLIST_FILE = ../ios/ExampleApp/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AC57B8D9EC88D1BC820C7419 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = "../ios/ExampleApp/screenshare-extension.entitlements";
				INFOPLIST_FILE = "../ios/ExampleApp/Info-screenshare-extension.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = co.ujet.qa.ExampleApp.ScreenShareExtension;
				PROVISIONING_PROFILE_SPECIFIER = "match Development co.ujet.qa.ExampleApp.ScreenShareExtension";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B69A8420EA81DFC1A838A0BF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = ../ios/ExampleApp/app.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				INFOPLIST_FILE = ../ios/ExampleApp/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		C425BDE89D0C96534D273F9B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 8694345EB8;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				MARKETING_VERSION = 3.0.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = co.ujet.qa.ExampleApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "match Development co.ujet.qa.ExampleApp";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		FE4811CD64AF59D7C1E2E2BB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = ../ios/ExampleApp/app.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				INFOPLIST_FILE = ../ios/ExampleApp/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		5DB548D70D1BBC8AA92C9F0C /* Build configuration list for PBXNativeTarget "Empty" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FE4811CD64AF59D7C1E2E2BB /* Debug */,
				B69A8420EA81DFC1A838A0BF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		73A70D16F69D798FC49217D0 /* Build configuration list for PBXProject "ExampleApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C425BDE89D0C96534D273F9B /* Debug */,
				1EFA7DE0C166B5B12443A164 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		C8B23987A45B2F4F51621AA0 /* Build configuration list for PBXNativeTarget "ChatSwiftUI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2AEBFEA520CB213A66FF7828 /* Debug */,
				196EB923FC4ABC15C3C9953A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		D351009F7512CB3363630CDB /* Build configuration list for PBXNativeTarget "screenshare-extension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AC57B8D9EC88D1BC820C7419 /* Debug */,
				6FAA55CE68DD1B20DAE3DD29 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		E20B7A97B34D5BD9877BF1C4 /* Build configuration list for PBXNativeTarget "Chat" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				591C02B226A2274D5CAB7688 /* Debug */,
				9B5FB642D3B8446BB52E1046 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		4E23964B16344D529DF143E2 /* XCLocalSwiftPackageReference "../../spm" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../../spm;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		12A634332E758F6456D249FC /* CCAIKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = CCAIKit;
		};
		388BDCC85C64693362F5562F /* CCAIKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = CCAIKit;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = E6FC2ABCE62557C86BAC1580 /* Project object */;
}
