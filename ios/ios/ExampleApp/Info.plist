<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CBIOBroadcastExtension</key>
	<string>co.ujet.qa.ExampleApp.ScreenShareExtension</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>co.ujet.qa.ExampleApp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ccai</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<integer>1</integer>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Bluetooth Usage Description</string>
	<key>NSCameraUsageDescription</key>
	<string>Camera Usage Description</string>
	<key>NSFaceIDUsageDescription</key>
	<string>FaceID Usage Description</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location Usage Description</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Microphone Usage Description</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Photo Library Usage Description</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>remote-notification</string>
		<string>voip</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
</dict>
</plist>
