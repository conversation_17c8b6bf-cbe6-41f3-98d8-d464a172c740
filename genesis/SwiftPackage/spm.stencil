// swift-tools-version:5.7

import PackageDescription

let package = Package(
  name: "CCAIKit",
  platforms: [
    .iOS(.{{ platform_version }})
  ],
  products: [
    .library(
      name: "CCAIKit",
      targets: ["WrapperCC<PERSON><PERSON>Kit", "WrapperCCAIChat", "WrapperCCAIChatRed"]),
    .library(
      name: "CCAIScreenShare",
      targets: ["WrapperCCAIScreenShare"]),
  ],
  dependencies: [
    .package(url: "https://github.com/twilio/conversations-ios", .upToNextMinor(from: "{{ twilio_conversations }}")),
    .package(url: "https://github.com/cobrowseio/cobrowse-sdk-ios-binary", .upToNextMinor(from: "{{ cobrowse_io }}")),
  ],
  targets: [
    .binaryTarget(
      name: "CCAIKit",
      {% if local %}
      path: "CCAIKit.xcframework"
      {% else %}
      url: "https://{{ base_url }}/{{ ujet }}/CCAIKit.xcframework.zip",
      checksum: "{{ checksum_ccaikit }}"
      {% endif %}
    ),

    .binaryTarget(
      name: "CCAIChat",
      {% if local %}
      path: "CCAIChat.xcframework"
      {% else %}
      url: "https://{{ base_url }}/{{ ujet }}/CCAIChat.xcframework.zip",
      checksum: "{{ checksum_ccaichat }}"
      {% endif %}
    ),

    .binaryTarget(
      name: "CCAIChatRed",
      {% if local %}
      path: "CCAIChatRed.xcframework"
      {% else %}
      url: "https://{{ base_url }}/{{ ujet }}/CCAIChatRed.xcframework.zip",
      checksum: "{{ checksum_ccaichatred }}"
      {% endif %}
    ),

    .binaryTarget(
      name: "CCAIScreenShare",
      {% if local %}
      path: "CCAIScreenShare.xcframework"
      {% else %}
      url: "https://{{ base_url }}/{{ ujet }}/CCAIScreenShare.xcframework.zip",
      checksum: "{{ checksum_ccaiscreenshare }}"
      {% endif %}
    ),

    .target(
      name: "WrapperCCAIKit",
      dependencies: [
        "CCAIKit"
      ]
    ),

    .target(
      name: "WrapperCCAIChat",
      dependencies: [
        "CCAIKit",
        "CCAIChat"
      ]
    ),

    .target(
      name: "WrapperCCAIChatRed",
      dependencies: [
        "CCAIKit",
        "CCAIChat",
        "CCAIChatRed",
        .product(name: "TwilioConversationsClient", package: "conversations-ios")
      ]
    ),

    .target(
      name: "WrapperCCAIScreenShare",
      dependencies: [
        "CCAIKit",
        "CCAIScreenShare",
        .product(name: "CobrowseSDK", package: "cobrowse-sdk-ios-binary")
      ]
    )
  ]
)
