{% set app_id %}co.ujet.qa.ExampleApp{% endset %}
name: ExampleApp
options:
  deploymentTarget:
    iOS: {{ deployment_target }}
packages:
  CCAIKit:
    path: ../Distribute
  CobrowseSDK:
    github: cobrowseio/cobrowse-sdk-ios-binary
    from: {{ cobrowse_io }}
settings:
  base:
    CODE_SIGN_STYLE: Manual
    CURRENT_PROJECT_VERSION: {{ build_number }}
    DEBUG_INFORMATION_FORMAT: "dwarf-with-dsym"
    DEVELOPMENT_TEAM: 8694345EB8
    GENERATE_INFOPLIST_FILE: true
    MARKETING_VERSION: {{ ujet }}
    PRODUCT_BUNDLE_IDENTIFIER: {{ app_id }}
  configs:
    debug:
      PROVISIONING_PROFILE_SPECIFIER: match Development {{ app_id }}
    release:
      PROVISIONING_PROFILE_SPECIFIER: match AppStore {{ app_id }}
targets:
  Empty:
    templates: [App]
    
  ChatRed:
    templates: [App]
    dependencies:
      - package: CCAIKit
      - target: screenshare-extension
        embed: true
        codeSign: false
        buildPhase:
          copyFiles:
            destination: plugins
  
  ChatSwiftUI:
    templates: [App]
    dependencies:
      - package: CCAIKit
      - target: screenshare-extension
        embed: true
        codeSign: false
        buildPhase:
          copyFiles:
            destination: plugins

  screenshare-extension:
    templates: [Extension]
    dependencies:
      - package: CobrowseSDK
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: {{ app_id }}.ScreenShareExtension
      configs:
        debug:
          PROVISIONING_PROFILE_SPECIFIER: match Development {{ app_id }}.ScreenShareExtension
        release:
          PROVISIONING_PROFILE_SPECIFIER: match AppStore {{ app_id }}.ScreenShareExtension
    
targetTemplates:
  App:
    type: application
    platform: iOS
    scheme: {}
    sources:
      - path: ${target_name}
    info:
      path: Info.plist
      properties:
        CBIOBroadcastExtension: {{ app_id }}.ScreenShareExtension
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleURLTypes:
          - CFBundleTypeRole: Editor
            CFBundleURLName: {{ app_id }}
            CFBundleURLSchemes:
              - ccai
        CFBundleVersion: {{ build_number }}
        NSCameraUsageDescription: "Camera Usage Description"
        NSFaceIDUsageDescription: "FaceID Usage Description"
        NSMicrophoneUsageDescription: "Microphone Usage Description"
        NSPhotoLibraryUsageDescription: "Photo Library Usage Description"
        NSBluetoothAlwaysUsageDescription: "Bluetooth Usage Description"
        NSLocationWhenInUseUsageDescription: "Location Usage Description"
        UIBackgroundModes:
          - audio
          - remote-notification
          - voip
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
    entitlements:
      path: app.entitlements
      properties:
        aps-environment: development
        keychain-access-groups: 
          - $(AppIdentifierPrefix)io.cobrowse

  Extension:
    type: app-extension
    platform: iOS
    sources:
      - path: ../ScreenShareExtension
        excludes:
          - Info.plist
    info:
      path: Info-${target_name}.plist
      properties:
        CFBundleDisplayName: ScreenShareExtension
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleVersion: {{ build_number }}
        NSExtension:
          NSExtensionPointIdentifier: com.apple.broadcast-services-upload
          NSExtensionPrincipalClass: SampleHandler
          RPBroadcastProcessMode: RPBroadcastProcessModeSampleBuffer
    entitlements:
      path: ${target_name}.entitlements
      properties:
        keychain-access-groups: 
          - $(AppIdentifierPrefix)io.cobrowse
