import SwiftUI
{% if type == 'Chat' or type == 'ChatSwiftUI' %}
import CCAIKit
import CC<PERSON><PERSON><PERSON>
{% endif %}
{% if type == 'ChatSwiftUI' %}
import CCAIChatSwiftUI
{% endif %}

struct ContentView: View {
    var body: some View {
        VStack {
            Text("Hello, world!")
        }
        .padding()
    }
}

@main
struct ExampleApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}

#Preview {
    ContentView()
}
