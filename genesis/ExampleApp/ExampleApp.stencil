import SwiftUI
{% if type == 'ChatRed' or type == 'ChatSwiftUI' %}
import CCAIKit
import CC<PERSON><PERSON>hat
import CC<PERSON><PERSON>hatRed
{% endif %}
{% if type == 'ChatSwiftUI' %}
import CC<PERSON><PERSON>hatSwiftUI
{% endif %}

struct ContentView: View {
    var body: some View {
        VStack {
            Text("Hello, world!")
        }
        .padding()
    }
}

@main
struct ExampleApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}

#Preview {
    ContentView()
}
